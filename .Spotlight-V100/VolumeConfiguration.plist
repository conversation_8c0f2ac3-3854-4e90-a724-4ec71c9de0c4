<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Annotations</key>
	<dict>
		<key>Creation_Predicates</key>
		<dict>
			<key>false</key>
			<integer>0</integer>
			<key>fstype.msdos</key>
			<integer>1</integer>
			<key>has.uuid</key>
			<integer>1</integer>
			<key>interconnect.secure digital</key>
			<integer>1</integer>
			<key>is.ROSP</key>
			<integer>0</integer>
			<key>is.alreadyindexed</key>
			<integer>0</integer>
			<key>is.apfssnapshot</key>
			<integer>0</integer>
			<key>is.automount</key>
			<integer>0</integer>
			<key>is.backupstore</key>
			<integer>0</integer>
			<key>is.backupvolume</key>
			<integer>0</integer>
			<key>is.bootablevolume</key>
			<integer>0</integer>
			<key>is.cameramedia</key>
			<integer>0</integer>
			<key>is.diskimage</key>
			<integer>0</integer>
			<key>is.dontbrowse</key>
			<integer>0</integer>
			<key>is.ejectable</key>
			<integer>1</integer>
			<key>is.enterprise</key>
			<integer>0</integer>
			<key>is.external</key>
			<integer>0</integer>
			<key>is.externalvolumes.defaultoff</key>
			<integer>0</integer>
			<key>is.externalvolumes.ignore</key>
			<integer>0</integer>
			<key>is.filevault</key>
			<integer>0</integer>
			<key>is.forcedefaultindex</key>
			<integer>0</integer>
			<key>is.forcefsonly</key>
			<integer>0</integer>
			<key>is.home</key>
			<integer>0</integer>
			<key>is.internal</key>
			<integer>1</integer>
			<key>is.ipod</key>
			<integer>0</integer>
			<key>is.iseffectiverootfs</key>
			<integer>0</integer>
			<key>is.local</key>
			<integer>1</integer>
			<key>is.lowdiskspace</key>
			<integer>0</integer>
			<key>is.network</key>
			<integer>0</integer>
			<key>is.pairedIndex</key>
			<integer>0</integer>
			<key>is.pairedTarget</key>
			<integer>0</integer>
			<key>is.preboot</key>
			<integer>0</integer>
			<key>is.quarantined</key>
			<integer>0</integer>
			<key>is.readonly</key>
			<integer>0</integer>
			<key>is.removable</key>
			<integer>1</integer>
			<key>is.rootfs</key>
			<integer>0</integer>
			<key>is.safeboot</key>
			<integer>0</integer>
			<key>is.syntheticmount</key>
			<integer>0</integer>
			<key>is.tinyvolume</key>
			<integer>0</integer>
			<key>is.windowsbootablevolume</key>
			<integer>0</integer>
			<key>is.xsan</key>
			<integer>0</integer>
			<key>policy.location.volume</key>
			<integer>1</integer>
			<key>self.appleinternal</key>
			<integer>0</integer>
			<key>self.server</key>
			<integer>0</integer>
			<key>status.neverindex</key>
			<integer>0</integer>
			<key>supports.catsearch</key>
			<integer>0</integer>
			<key>supports.fileids</key>
			<integer>0</integer>
			<key>supports.volfs</key>
			<integer>0</integer>
			<key>true</key>
			<integer>1</integer>
			<key>uuid.8e4a5ce8-c026-3ad4-ab61-c7bac60eb9c5</key>
			<integer>1</integer>
		</dict>
		<key>DebugKey1</key>
		<string>2025-05-25 09:00:25 +0000 3</string>
		<key>DefaultStore_EffectiveSearch</key>
		<integer>3</integer>
		<key>DefaultStore_RequestedSearch</key>
		<integer>3</integer>
	</dict>
	<key>ConfigurationCreationDate</key>
	<date>2025-05-25T09:00:25Z</date>
	<key>ConfigurationCreationVersion</key>
	<string>Version 15.5 (Build 24F74)</string>
	<key>ConfigurationModificationDate</key>
	<date>2025-07-22T04:30:26Z</date>
	<key>ConfigurationModificationVersion</key>
	<string>Version 15.5 (Build 24F74)</string>
	<key>ConfigurationVolumeUUID</key>
	<string>8E4A5CE8-C026-3AD4-AB61-C7BAC60EB9C5</string>
	<key>ConfigurationWriteback</key>
	<false/>
	<key>Exclusions</key>
	<array/>
	<key>Options</key>
	<dict>
		<key>ConfigurationType</key>
		<string>Default</string>
	</dict>
	<key>Stores</key>
	<dict>
		<key>884DAFAC-4D57-4AF8-BD87-501ABD7D65A4</key>
		<dict>
			<key>CreationDate</key>
			<date>2025-05-25T09:00:25Z</date>
			<key>CreationVersion</key>
			<string>Version 15.5 (Build 24F74)</string>
			<key>CreationVolumeFlags</key>
			<integer>360413418131292385</integer>
			<key>IndexVersion</key>
			<integer>99</integer>
			<key>PartialPath</key>
			<string>/</string>
			<key>PolicyDate</key>
			<date>2025-07-22T04:30:26Z</date>
			<key>PolicyLevel</key>
			<string>kMDConfigSearchLevelReadWrite</string>
			<key>PolicyProcess</key>
			<string>STORE_ADD</string>
			<key>PolicyVersion</key>
			<string>Version 15.5 (Build 24F74)</string>
			<key>UpdateVolumeFlags</key>
			<integer>360413418131292385</integer>
		</dict>
	</dict>
</dict>
</plist>
