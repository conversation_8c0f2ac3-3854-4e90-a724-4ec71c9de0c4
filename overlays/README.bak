Introduction
============

This directory contains Device Tree overlays. Device Tree makes it possible
to support many hardware configurations with a single kernel and without the
need to explicitly load or blacklist kernel modules. Note that this isn't a
"pure" Device Tree configuration (c.f. MACH_BCM2835) - some on-board devices
are still configured by the board support code, but the intention is to
eventually reach that goal.

On Raspberry Pi, Device Tree usage is controlled from /boot/config.txt. By
default, the Raspberry Pi kernel boots with device tree enabled. You can
completely disable DT usage (for now) by adding:

    device_tree=

to your config.txt, which should cause your Pi to revert to the old way of
doing things after a reboot.

In /boot you will find a .dtb for each base platform. This describes the
hardware that is part of the Raspberry Pi board. The loader (start.elf and its
siblings) selects the .dtb file appropriate for the platform by name, and reads
it into memory. At this point, all of the optional interfaces (i2c, i2s, spi)
are disabled, but they can be enabled using Device Tree parameters:

    dtparam=i2c=on,i2s=on,spi=on

However, this shouldn't be necessary in many use cases because loading an
overlay that requires one of those interfaces will cause it to be enabled
automatically, and it is advisable to only enable interfaces if they are
needed.

Configuring additional, optional hardware is done using Device Tree overlays
(see below).

GPIO numbering uses the hardware pin numbering scheme (aka BCM scheme) and
not the physical pin numbers.

raspi-config
============

The Advanced Options section of the raspi-config utility can enable and disable
Device Tree use, as well as toggling the I2C and SPI interfaces. Note that it
is possible to both enable an interface and blacklist the driver, if for some
reason you should want to defer the loading.

Modules
=======

As well as describing the hardware, Device Tree also gives enough information
to allow suitable driver modules to be located and loaded, with the corollary
that unneeded modules are not loaded. As a result it should be possible to
remove lines from /etc/modules, and /etc/modprobe.d/raspi-blacklist.conf can
have its contents deleted (or commented out).

Using Overlays
==============

Overlays are loaded using the "dtoverlay" config.txt setting. As an example,
consider I2C Real Time Clock drivers. In the pre-DT world these would be loaded
by writing a magic string comprising a device identifier and an I2C address to
a special file in /sys/class/i2c-adapter, having first loaded the driver for
the I2C interface and the RTC device - something like this:

    modprobe i2c-bcm2835
    modprobe rtc-ds1307
    echo ds1307 0x68 > /sys/class/i2c-adapter/i2c-1/new_device

With DT enabled, this becomes a line in config.txt:

    dtoverlay=i2c-rtc,ds1307

This causes the file /boot/overlays/i2c-rtc.dtbo to be loaded and a "node"
describing the DS1307 I2C device to be added to the Device Tree for the Pi. By
default it usees address 0x68, but this can be modified with an additional DT
parameter:

    dtoverlay=i2c-rtc,ds1307,addr=0x68

Parameters usually have default values, although certain parameters are
mandatory. See the list of overlays below for a description of the parameters
and their defaults.

Making new Overlays based on existing Overlays
==============================================

Recent overlays have been designed in a more general way, so that they can be
adapted to hardware by changing their parameters. When you have additional
hardware with more than one device of a kind, you end up using the same overlay
multiple times with other parameters, e.g.

    # 2 CAN FD interfaces on spi but with different pins
    dtoverlay=mcp251xfd,spi0-0,interrupt=25
    dtoverlay=mcp251xfd,spi0-1,interrupt=24

    # a realtime clock on i2c
    dtoverlay=i2c-rtc,pcf85063

While this approach does work, it requires knowledge about the hardware design.
It is more feasible to simplify things for the end user by providing a single
overlay as it is done the traditional way.

A new overlay can be generated by using ovmerge utility.
https://github.com/raspberrypi/utils/blob/master/ovmerge/ovmerge

To generate an overlay for the above configuration we pass the configuration
to ovmerge and add the -c flag.

    ovmerge -c mcp251xfd-overlay.dts,spi0-0,interrupt=25 \
               mcp251xfd-overlay.dts,spi0-1,interrupt=24 \
               i2c-rtc-overlay.dts,pcf85063 \
    >> merged-overlay.dts

The -c option writes the command above as a comment into the overlay as
a marker that this overlay is generated and how it was generated.
After compiling the overlay it can be loaded in a single line.

    dtoverlay=merged

It does the same as the original configuration but without parameters.

The Overlay and Parameter Reference
===================================

N.B. When editing this file, please preserve the indentation levels to make it
simple to parse programmatically. NO HARD TABS.


Name:   <The base DTB>
Info:   Configures the base Raspberry Pi hardware
Load:   <loaded automatically>
Params:
        act_led_trigger         Choose which activity the LED tracks.
                                Use "heartbeat" for a nice load indicator.
                                (default "mmc")

        act_led_activelow       Set to "on" to invert the sense of the LED
                                (default "off")
                                N.B. For Pi 3B, 3B+, 3A+ and 4B, use the act-led
                                overlay.

        act_led_gpio            Set which GPIO to use for the activity LED
                                (in case you want to connect it to an external
                                device)
                                (default "16" on a non-Plus board, "47" on a
                                Plus or Pi 2)
                                N.B. For Pi 3B, 3B+, 3A+ and 4B, use the act-led
                                overlay.

        ant1                    Select antenna 1 (default). CM4/5 only.

        ant2                    Select antenna 2. CM4/5 only.

        noant                   Disable both antennas. CM4/5 only.

        noanthogs               Disable the GPIO hogs on the antenna controls
                                so they can be controlled at runtime. Note that
                                using this parameter without suitable OS
                                support will result in attenuated WiFi and
                                Bluetooth signals. CM4/5 only.

        audio                   Set to "on" to enable the onboard ALSA audio
                                interface (default "off")

        axiperf                 Set to "on" to enable the AXI bus performance
                                monitors.
                                See /sys/kernel/debug/raspberrypi_axi_monitor
                                for the results.

        bdaddr                  Set an alternative Bluetooth address (BDADDR).
                                The value should be a 6-byte hexadecimal value,
                                with or without colon separators, written least-
                                significant-byte first. For example,
                                bdaddr=06:05:04:03:02:01
                                will set the BDADDR to 01:02:03:04:05:06.

        button_debounce         Set the debounce delay (in ms) on the power/
                                shutdown button (default 50ms)

        cam0_reg                Controls CAM 0 regulator.
                                Disabled by default on CM1 & 3.
                                Enabled by default on all other boards.

        cam0_reg_gpio           Set GPIO for CAM 0 regulator.
                                NB override switches to the normal GPIO driver,
                                even if the original was on the GPIO expander.

        cam1_reg                Controls CAM 1 regulator.
                                Disabled by default on CM1 & 3.
                                Enabled by default on all other boards.

        cam1_reg_gpio           Set GPIO for CAM 1 regulator.
                                NB override switches to the normal GPIO driver,
                                even if the original was on the GPIO expander.

        cam0_sync               Enable a GPIO to reflect frame sync from CSI0,
                                going high on frame start, and low on frame end.

        cam0_sync_inverted      Enable a GPIO to reflect frame sync from CSI0
                                going low on frame start, and high on frame end.

        cam1_sync               Enable a GPIO to reflect frame sync from CSI1,
                                going high on frame start, and low on frame end.

        cam1_sync_inverted      Enable a GPIO to reflect frame sync from CSI1
                                going low on frame start, and high on frame end.

        cooling_fan             Enables the Pi 5 cooling fan (enabled
                                automatically by the firmware)

        drm_fb0_rp1_dpi         Assign /dev/fb0 to the RP1 DPI output

        drm_fb0_rp1_dsi0        Assign /dev/fb0 to the RP1 DSI0 output

        drm_fb0_rp1_dsi1        Assign /dev/fb0 to the RP1 DSI1 output

        drm_fb0_vc4             Assign /dev/fb0 to the vc4 outputs

        drm_fb1_rp1_dpi         Assign /dev/fb1 to the RP1 DPI output

        drm_fb1_rp1_dsi0        Assign /dev/fb1 to the RP1 DSI0 output

        drm_fb1_rp1_dsi1        Assign /dev/fb1 to the RP1 DSI1 output

        drm_fb1_vc4             Assign /dev/fb1 to the vc4 outputs

        drm_fb2_rp1_dpi         Assign /dev/fb2 to the RP1 DPI output

        drm_fb2_rp1_dsi0        Assign /dev/fb2 to the RP1 DSI0 output

        drm_fb2_rp1_dsi1        Assign /dev/fb2 to the RP1 DSI1 output

        drm_fb2_vc4             Assign /dev/fb2 to the vc4 outputs

        dwc2_dr_mode            Set dwc2 USB controller driver mode.
                                Default: "otg"
                                Legal values: "host", "peripheral", "otg".

        eee                     Enable Energy Efficient Ethernet support for
                                compatible devices (default "on"). See also
                                "tx_lpi_timer". Pi3B+ only.

        eth_downshift_after     Set the number of auto-negotiation failures
                                after which the 1000Mbps modes are disabled.
                                Legal values are 2, 3, 4, 5 and 0, where
                                0 means never downshift (default 2). Pi3B+ only.

        eth_led0                Set mode of LED0 - amber on Pi3B+ (default "1"),
                                green on Pi4/5 (default "0").
                                The legal values are:

                                Pi3B+

                                0=link/activity          1=link1000/activity
                                2=link100/activity       3=link10/activity
                                4=link100/1000/activity  5=link10/1000/activity
                                6=link10/100/activity    14=off    15=on

                                Pi4/5

                                0=Speed/Activity         1=Speed
                                2=Flash activity         3=FDX
                                4=Off                    5=On
                                6=Alt                    7=Speed/Flash
                                8=Link                   9=Activity

        eth_led1                Set mode of LED1 - green on Pi3B+ (default "6"),
                                amber on Pi4/5 (default "8"). See eth_led0 for
                                legal values.

        eth_max_speed           Set the maximum speed a link is allowed
                                to negotiate. Legal values are 10, 100 and
                                1000 (default 1000). Pi3B+ only.

        fan_temp0               Temperature threshold (in millicelcius) for
                                1st cooling level (default 50000). Pi5 only.
        fan_temp0_hyst          Temperature hysteresis (in millicelcius) for
                                1st cooling level (default 5000). Pi5 only.
        fan_temp0_speed         Fan PWM setting for 1st cooling level (0-255,
                                default 75). Pi5 only.
        fan_temp1               Temperature threshold (in millicelcius) for
                                2nd cooling level (default 60000). Pi5 only.
        fan_temp1_hyst          Temperature hysteresis (in millicelcius) for
                                2nd cooling level (default 5000). Pi5 only.
        fan_temp1_speed         Fan PWM setting for 2nd cooling level (0-255,
                                default 125). Pi5 only.
        fan_temp2               Temperature threshold (in millicelcius) for
                                3rd cooling level (default 67500). Pi5 only.
        fan_temp2_hyst          Temperature hysteresis (in millicelcius) for
                                3rd cooling level (default 5000). Pi5 only.
        fan_temp2_speed         Fan PWM setting for 3rd cooling level (0-255,
                                default 175). Pi5 only.
        fan_temp3               Temperature threshold (in millicelcius) for
                                4th cooling level (default 75000). Pi5 only.
        fan_temp3_hyst          Temperature hysteresis (in millicelcius) for
                                4th cooling level (default 5000). Pi5 only.
        fan_temp3_speed         Fan PWM setting for 4th cooling level (0-255,
                                default 250). Pi5 only.

        hdmi                    Set to "off" to disable the HDMI interface
                                (default "on")

        i2c                     An alias for i2c_arm

        i2c_arm                 Set to "on" to enable the ARM's i2c interface
                                (default "off")

        i2c_arm_baudrate        Set the baudrate of the ARM's i2c interface
                                (default "100000")

        i2c_baudrate            An alias for i2c_arm_baudrate

        i2c_csi_dsi             Set to "on" to enable the i2c_csi_dsi interface
                                The I2C bus and GPIOs are platform specific:
                                  B rev 1:
                                    i2c-1 on 2 & 3
                                  B rev 2, B+, CM, Zero, Zero W, 2B, CM2, CM3,
                                  CM4S:
                                    i2c-0 on 28 & 29
                                  3B, 3B+, Zero 2W, 4B, 400, CM4:
                                    i2c-0 on 44 & 45
                                  5, 500:
                                    i2c-11/i2c-4 on 40 & 41
                                  CM5 on CM5IO:
                                    i2c-0 on 0 & 1
                                  CM5 on CM4IO:
                                    i2c-10/i2c-6 on 38 & 39

        i2c_csi_dsi0            Set to "on" to enable the i2c_csi_dsi0 interface
                                The I2C bus and GPIOs are platform specific:
                                  B rev 1 & 2, B+, CM, Zero, Zero W, 2B, CM2,
                                  CM3, CM4S, 3B, 3B+, Zero 2W, 4B, 400, CM4,
                                  CM5 on CM4IO:
                                    i2c-0 on 0 & 1
                                  5, 500, CM5 on CM5IO:
                                    i2c-10/i2c-6 on 38 & 39

        i2c_csi_dsi1            A Pi 5 family-specific alias for i2c_csi_dsi.

        i2c_vc                  Set to "on" to enable the i2c interface
                                usually reserved for the VideoCore processor
                                (default "off")

        i2c_vc_baudrate         Set the baudrate of the VideoCore i2c interface
                                (default "100000")

        i2s                     Set to "on" to enable the i2s interface
                                (default "off")

        i2s_dma4                Use to enable 40-bit DMA on the i2s interface
                                (the assigned value doesn't matter)
                                (2711 only)

        krnbt                   Set to "off" to disable autoprobing of Bluetooth
                                driver without need of hciattach/btattach
                                (default "on")

        krnbt_baudrate          Set the baudrate of the PL011 UART when used
                                with krnbt=on

        nvme                    Alias for "pciex1" (2712 only)

        nvmem_cust_rw           Allow read/write access to customer otp

        nvmem_mac_rw            Allow read/write access to mac addresses otp

        nvmem_priv_rw           Allow read/write access to customer private otp

        pcie                    Set to "off" to disable the PCIe interface
                                (default "on")
                                (2711 only, but not applicable on CM4S)
                                N.B. USB-A ports on 4B are subsequently disabled

        pcie_tperst_clk_ms      Add N milliseconds between PCIe reference clock
                                activation and PERST# deassertion
                                (CM4 and 2712, default "0")

        pciex1                  Set to "on" to enable the external PCIe link
                                (2712 only, default "off")

        pciex1_gen              Sets the PCIe "GEN"/speed for the external PCIe
                                link (2712 only, default "2")

        pciex1_no_l0s           Set to "on" to disable ASPM L0s on the external
                                PCIe link for devices that have broken
                                implementations (2712 only, default "off")

        pciex1_tperst_clk_ms    Alias for pcie_tperst_clk_ms
                                (2712 only, default "0")

        pwr_led_trigger
        pwr_led_activelow
        pwr_led_gpio
                                As for act_led_*, but using the PWR LED.
                                Not available on Model A/B boards.

        random                  Set to "on" to enable the hardware random
                                number generator (default "on")

        rtc                     Set to "off" to disable the onboard Real Time
                                Clock (2712 only, default "on")

        rtc_bbat_vchg           Set the RTC backup battery charging voltage in
                                microvolts. If set to 0 or not specified, the
                                trickle charger is disabled.
                                (2712 only, default "0")

        sd                      Set to "off" to disable the SD card (or eMMC on
                                non-lite SKU of CM4).
                                (default "on")

        sd_cqe                  Modify Command Queuing behaviour on the main SD
                                interface. Legal values are:
                                0: disable CQ
                                1: allow CQ for known-good SD A2 cards, and all
                                   eMMC cards
                                2: allow CQ for all SD A2 cards that aren't
                                   known-bad, and all eMMC cards.
                                (2712 only, default "1")

        sd_overclock            Clock (in MHz) to use when the MMC framework
                                requests 50MHz

        sd_poll_once            Looks for a card once after booting. Useful
                                for network booting scenarios to avoid the
                                overhead of continuous polling. N.B. Using
                                this option restricts the system to using a
                                single card per boot (or none at all).
                                (default off)

        sd_force_pio            Disable DMA support for SD driver (default off)

        sd_pio_limit            Number of blocks above which to use DMA for
                                SD card (default 1)

        sd_debug                Enable debug output from SD driver (default off)

        sdio_overclock          Clock (in MHz) to use when the MMC framework
                                requests 50MHz for the SDIO/WLAN interface.

        spi                     Set to "on" to enable the spi interfaces
                                (default "off")

        spi_dma4                Use to enable 40-bit DMA on spi interfaces
                                (the assigned value doesn't matter)
                                (2711 only)

        strict_gpiod            Return GPIOs to inputs when they are released.
                                If using the gpiod utilities, it is necessary
                                to keep a gpioset running (e.g. with
                                --mode=wait) in order for an output value to
                                persist.

        suspend                 Make the power button trigger a suspend rather
                                than a power-off (2712 only, default "off")

        tx_lpi_timer            Set the delay in microseconds between going idle
                                and entering the low power state (default 600).
                                Requires EEE to be enabled - see "eee".

        uart0                   Set to "off" to disable uart0 (default "on")

        uart0_console           Move the kernel boot console to UART0 on pins
                                6, 8 and 10 of the 40-way header (2712 only,
                                default "off")

        uart1                   Set to "on" or "off" to enable or disable uart1
                                (default varies)

        watchdog                Set to "on" to enable the hardware watchdog
                                (default "off")

        wifiaddr                Set an alternative WiFi MAC address.
                                The value should be a 6-byte hexadecimal value,
                                with or without colon separators, written in the
                                natural (big-endian) order.

        N.B. It is recommended to only enable those interfaces that are needed.
        Leaving all interfaces enabled can lead to unwanted behaviour (i2c_vc
        interfering with Pi Camera, I2S and SPI hogging GPIO pins, etc.)
        Note also that i2c, i2c_arm and i2c_vc are aliases for the physical
        interfaces i2c0 and i2c1. Use of the numeric variants is still possible
        but deprecated because the ARM/VC assignments differ between board
        revisions. The same board-specific mapping applies to i2c_baudrate,
        and the other i2c baudrate parameters.


Name:   act-led
Info:   Pi 3B, 3B+, 3A+ and 4B use a GPIO expander to drive the LEDs which can
        only be accessed from the VPU. There is a special driver for this with a
        separate DT node, which has the unfortunate consequence of breaking the
        act_led_gpio and act_led_activelow dtparams.
        This overlay changes the GPIO controller back to the standard one and
        restores the dtparams.
Load:   dtoverlay=act-led,<param>=<val>
Params: activelow               Set to "on" to invert the sense of the LED
                                (default "off")

        gpio                    Set which GPIO to use for the activity LED
                                (in case you want to connect it to an external
                                device)
                                REQUIRED


Name:   adafruit-st7735r
Info:   Overlay for the SPI-connected Adafruit 1.8" 160x128 or 128x128 displays,
        based on the ST7735R chip.
        This overlay uses the newer DRM/KMS "Tiny" driver.
Load:   dtoverlay=adafruit-st7735r,<param>=<val>
Params: 128x128                 Select the 128x128 driver (default 160x128)
        rotate                  Display rotation {0,90,180,270} (default 90)
        speed                   SPI bus speed in Hz (default 4000000)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)
        led_pin                 GPIO used to control backlight (default 18)


Name:   adafruit18
Info:   Overlay for the SPI-connected Adafruit 1.8" display (based on the
        ST7735R chip). It includes support for the "green tab" version.
        This overlay uses the older fbtft driver.
Load:   dtoverlay=adafruit18,<param>=<val>
Params: green                   Use the adafruit18_green variant.
        rotate                  Display rotation {0,90,180,270}
        speed                   SPI bus speed in Hz (default 4000000)
        fps                     Display frame rate in Hz
        bgr                     Enable BGR mode (default off)
        debug                   Debug output level {0-7}
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)
        led_pin                 GPIO used to control backlight (default 18)


Name:   adau1977-adc
Info:   Overlay for activation of ADAU1977 ADC codec over I2C for control
        and I2S for data.
Load:   dtoverlay=adau1977-adc
Params: <None>


Name:   adau7002-simple
Info:   Overlay for the activation of ADAU7002 stereo PDM to I2S converter.
Load:   dtoverlay=adau7002-simple,<param>=<val>
Params: card-name               Override the default, "adau7002", card name.


Name:   ads1015
Info:   Overlay for activation of Texas Instruments ADS1015 ADC over I2C
Load:   dtoverlay=ads1015,<param>=<val>
Params: addr                    I2C bus address of device. Set based on how the
                                addr pin is wired. (default=0x48 assumes addr
                                is pulled to GND)
        cha_enable              Enable virtual channel a. (default=true)
        cha_cfg                 Set the configuration for virtual channel a.
                                (default=4 configures this channel for the
                                voltage at A0 with respect to GND)
        cha_datarate            Set the datarate (samples/sec) for this channel.
                                (default=4 sets 1600 sps)
        cha_gain                Set the gain of the Programmable Gain
                                Amplifier for this channel. (default=2 sets the
                                full scale of the channel to 2.048 Volts)

        Channel (ch) parameters can be set for each enabled channel.
        A maximum of 4 channels can be enabled (letters a thru d).
        For more information refer to the device datasheet at:
        http://www.ti.com/lit/ds/symlink/ads1015.pdf


Name:   ads1115
Info:   Texas Instruments ADS1115 ADC
Load:   dtoverlay=ads1115,<param>[=<val>]
Params: addr                    I2C bus address of device. Set based on how the
                                addr pin is wired. (default=0x48 assumes addr
                                is pulled to GND)
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"
        cha_enable              Enable virtual channel a.
        cha_cfg                 Set the configuration for virtual channel a.
                                (default=4 configures this channel for the
                                voltage at A0 with respect to GND)
        cha_datarate            Set the datarate (samples/sec) for this channel.
                                (default=7 sets 860 sps)
        cha_gain                Set the gain of the Programmable Gain
                                Amplifier for this channel. (Default 1 sets the
                                full scale of the channel to 4.096 Volts)

        Channel parameters can be set for each enabled channel.
        A maximum of 4 channels can be enabled (letters a thru d).
        For more information refer to the device datasheet at:
        http://www.ti.com/lit/ds/symlink/ads1115.pdf


Name:   ads7846
Info:   ADS7846 Touch controller
Load:   dtoverlay=ads7846,<param>=<val>
Params: cs                      SPI bus Chip Select (default 1)
        speed                   SPI bus speed (default 2MHz, max 3.25MHz)
        penirq                  GPIO used for PENIRQ. REQUIRED
        penirq_pull             Set GPIO pull (default 0=none, 2=pullup)
        swapxy                  Swap x and y axis
        xmin                    Minimum value on the X axis (default 0)
        ymin                    Minimum value on the Y axis (default 0)
        xmax                    Maximum value on the X axis (default 4095)
        ymax                    Maximum value on the Y axis (default 4095)
        pmin                    Minimum reported pressure value (default 0)
        pmax                    Maximum reported pressure value (default 65535)
        xohms                   Touchpanel sensitivity (X-plate resistance)
                                (default 400)

        penirq is required and usually xohms (60-100) has to be set as well.
        Apart from that, pmax (255) and swapxy are also common.
        The rest of the calibration can be done with xinput-calibrator.
        See: github.com/notro/fbtft/wiki/FBTFT-on-Raspian
        Device Tree binding document:
        www.kernel.org/doc/Documentation/devicetree/bindings/input/ads7846.txt


Name:   adv7282m
Info:   Analog Devices ADV7282M analogue video to CSI2 bridge.
        Uses Unicam1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=adv7282m,<param>=<val>
Params: addr                    Overrides the I2C address (default 0x21)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default off)


Name:   adv728x-m
Info:   Analog Devices ADV728[0|1|2]-M analogue video to CSI2 bridges.
        This is a wrapper for adv7282m, and defaults to ADV7282M.
Load:   dtoverlay=adv728x-m,<param>=<val>
Params: addr                    Overrides the I2C address (default 0x21)
        adv7280m                Select ADV7280-M.
        adv7281m                Select ADV7281-M.
        adv7281ma               Select ADV7281-MA.
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default off)


Name:   akkordion-iqdacplus
Info:   Configures the Digital Dreamtime Akkordion Music Player (based on the
        OEM IQAudIO DAC+ or DAC Zero module).
Load:   dtoverlay=akkordion-iqdacplus,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                dtoverlay=akkordion-iqdacplus,24db_digital_gain
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   allo-boss-dac-pcm512x-audio
Info:   Configures the Allo Boss DAC audio cards.
Load:   dtoverlay=allo-boss-dac-pcm512x-audio,<param>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=allo-boss-dac-pcm512x-audio,
                                24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        slave                   Force Boss DAC into slave mode, using Pi a
                                master for bit clock and frame clock. Enable
                                with "dtoverlay=allo-boss-dac-pcm512x-audio,
                                slave"


Name:   allo-boss2-dac-audio
Info:   Configures the Allo Boss2 DAC audio card
Load:   dtoverlay=allo-boss2-dac-audio
Params: <None>


Name:   allo-digione
Info:   Configures the Allo Digione audio card
Load:   dtoverlay=allo-digione
Params: <None>


Name:   allo-katana-dac-audio
Info:   Configures the Allo Katana DAC audio card
Load:   dtoverlay=allo-katana-dac-audio
Params: <None>


Name:   allo-piano-dac-pcm512x-audio
Info:   Configures the Allo Piano DAC (2.0/2.1) audio cards.
        (NB. This initial support is for 2.0 channel audio ONLY! ie. stereo.
        The subwoofer outputs on the Piano 2.1 are not currently supported!)
Load:   dtoverlay=allo-piano-dac-pcm512x-audio,<param>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control.
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   allo-piano-dac-plus-pcm512x-audio
Info:   Configures the Allo Piano DAC (2.1) audio cards.
Load:   dtoverlay=allo-piano-dac-plus-pcm512x-audio,<param>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control.
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        glb_mclk                This option is only with Kali board. If enabled,
                                MCLK for Kali is used and PLL is disabled for
                                better voice quality. (default Off)


Name:   anyspi
Info:   Universal device tree overlay for SPI devices

        Just specify the SPI address and device name ("compatible" property).
        This overlay lacks any device-specific parameter support!

        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.

        Examples:
        1. SPI NOR flash on spi0.1, maximum SPI clock frequency 45MHz:
            dtoverlay=anyspi:spi0-1,dev="jedec,spi-nor",speed=45000000
        2. MCP3204 ADC on spi1.2, maximum SPI clock frequency 500kHz:
            dtoverlay=anyspi:spi1-2,dev="microchip,mcp3204"
Load:   dtoverlay=anyspi,<param>=<val>
Params: spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)
        dev                     Set device name to search compatible module
                                (string, required)
        speed                   Set SPI clock frequency in Hz
                                (integer, optional, default 500000)


Name:   apds9960
Info:   Configures the AVAGO APDS9960 digital proximity, ambient light, RGB and
        gesture sensor
Load:   dtoverlay=apds9960,<param>=<val>
Params: gpiopin                 GPIO used for INT (default 4)
        noints                  Disable the interrupt GPIO line.


Name:   applepi-dac
Info:   Configures the Orchard Audio ApplePi-DAC audio card
Load:   dtoverlay=applepi-dac
Params: <None>


Name:   arducam-64mp
Info:   Arducam 64MP camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=arducam-64mp,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Select lens driver state. Default is enabled,
                                but vcm=off will disable.


Name:   arducam-pivariety
Info:   Arducam Pivariety camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=arducam-pivariety,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   at86rf233
Info:   Configures the Atmel AT86RF233 802.15.4 low-power WPAN transceiver,
        connected to spi0.0
Load:   dtoverlay=at86rf233,<param>=<val>
Params: interrupt               GPIO used for INT (default 23)
        reset                   GPIO used for Reset (default 24)
        sleep                   GPIO used for Sleep (default 25)
        speed                   SPI bus speed in Hz (default 3000000)
        trim                    Fine tuning of the internal capacitance
                                arrays (0=+0pF, 15=+4.5pF, default 15)


Name:   audioinjector-addons
Info:   Configures the audioinjector.net audio add on soundcards
Load:   dtoverlay=audioinjector-addons,<param>=<val>
Params: non-stop-clocks         Keeps the clocks running even when the stream
                                is paused or stopped (default off)


Name:   audioinjector-bare-i2s
Info:   Configures the audioinjector.net audio bare i2s soundcard
Load:   dtoverlay=audioinjector-bare-i2s
Params: <None>


Name:   audioinjector-isolated-soundcard
Info:   Configures the audioinjector.net isolated soundcard
Load:   dtoverlay=audioinjector-isolated-soundcard
Params: <None>


Name:   audioinjector-ultra
Info:   Configures the audioinjector.net ultra soundcard
Load:   dtoverlay=audioinjector-ultra
Params: <None>


Name:   audioinjector-wm8731-audio
Info:   Configures the audioinjector.net audio add on soundcard
Load:   dtoverlay=audioinjector-wm8731-audio
Params: <None>


Name:   audiosense-pi
Info:   Configures the audiosense-pi add on soundcard
        For more information refer to
        https://gitlab.com/kakar0t/audiosense-pi
Load:   *********************pi
Params: <None>


Name:   audremap
Info:   Switches PWM sound output to GPIOs on the 40-pin header
Load:   dtoverlay=audremap,<param>=<val>
Params: swap_lr                 Reverse the channel allocation, which will also
                                swap the audio jack outputs (default off)
        enable_jack             Don't switch off the audio jack output. Does
                                nothing on BCM2711 (default off)
        pins_12_13              Select GPIOs 12 & 13 (default)
        pins_18_19              Select GPIOs 18 & 19
        pins_40_41              Select GPIOs 40 & 41 (not available on CM4, used
                                for other purposes)
        pins_40_45              Select GPIOs 40 & 45 (don't use on BCM2711 - the
                                pins are on different controllers)


Name:   audremap-pi5
Info:   On Raspberry Pi 5 / 500 /CM5, enable digital audio output
        and route it to GPIOs 12 & 13 on the 40-pin header
Load:   dtoverlay=audremap-pi5,<param>=<val>
Params: swap_lr                 Reverse the channel allocation (default off)
        pins_12_13              Select GPIOs 12 & 13 (default)
        pins_18_19              Not available; this will not enable audio out
        pins_40_41              Not available; this will not enable audio out
        pins_40_45              Not available; this will not enable audio out


Name:   balena-fin
Info:   Overlay that enables WLAN, Bluetooth and the GPIO expander on the
        balenaFin carrier board for the Raspberry Pi Compute Module 3/3+ Lite.
Load:   dtoverlay=balena-fin
Params: <None>


Name:   bcm2712d0
Info:   Overlay encapsulating the BCM2712 C0->D0 differences
Load:   dtoverlay=bcm2712d0
Params: <None>


Name:   bmp085_i2c-sensor
Info:   This overlay is now deprecated - see i2c-sensor
Load:   <Deprecated>


Name:   camera-mux-2port
Info:   Configures a 2 port camera multiplexer
        Note that currently ALL IMX290 modules share a common clock, therefore
        all modules will need to have the same clock frequency.
Load:   dtoverlay=camera-mux-2port,<param>=<val>
Params: cam0-arducam-64mp       Select Arducam64MP for camera on port 0
        cam0-imx219             Select IMX219 for camera on port 0
        cam0-imx258             Select IMX258 for camera on port 0
        cam0-imx290             Select IMX290 for camera on port 0
        cam0-imx477             Select IMX477 for camera on port 0
        cam0-imx519             Select IMX519 for camera on port 0
        cam0-imx708             Select IMX708 for camera on port 0
        cam0-ov2311             Select OV2311 for camera on port 0
        cam0-ov5647             Select OV5647 for camera on port 0
        cam0-ov64a40            Select OV64A40 for camera on port 0
        cam0-ov7251             Select OV7251 for camera on port 0
        cam0-ov9281             Select OV9281 for camera on port 0
        cam0-imx290-clk-freq    Set clock frequency for an IMX290 on port 0
        cam1-arducam-64mp       Select Arducam64MP for camera on port 1
        cam1-imx219             Select IMX219 for camera on port 1
        cam1-imx258             Select IMX258 for camera on port 1
        cam1-imx290             Select IMX290 for camera on port 1
        cam1-imx477             Select IMX477 for camera on port 1
        cam1-imx519             Select IMX519 for camera on port 1
        cam1-imx708             Select IMX708 for camera on port 1
        cam1-ov2311             Select OV2311 for camera on port 1
        cam1-ov5647             Select OV5647 for camera on port 1
        cam1-ov64a40            Select OV64A40 for camera on port 1
        cam1-ov7251             Select OV7251 for camera on port 1
        cam1-ov9281             Select OV9281 for camera on port 1
        cam1-imx290-clk-freq    Set clock frequency for an IMX290 on port 1
        cam0-sync-source        Set camera on port 0 as vsync source
        cam0-sync-sink          Set camera on port 0 as vsync sink
        cam1-sync-source        Set camera on port 1 as vsync source
        cam1-sync-sink          Set camera on port 1 as vsync sink

        cam0                    Connect the mux to CAM0 port (default is CAM1)


Name:   camera-mux-4port
Info:   Configures a 4 port camera multiplexer
        Note that currently ALL IMX290 modules share a common clock, therefore
        all modules will need to have the same clock frequency.
Load:   dtoverlay=camera-mux-4port,<param>=<val>
Params: cam0-arducam-64mp       Select Arducam64MP for camera on port 0
        cam0-imx219             Select IMX219 for camera on port 0
        cam0-imx258             Select IMX258 for camera on port 0
        cam0-imx290             Select IMX290 for camera on port 0
        cam0-imx477             Select IMX477 for camera on port 0
        cam0-imx519             Select IMX519 for camera on port 0
        cam0-imx708             Select IMX708 for camera on port 0
        cam0-ov2311             Select OV2311 for camera on port 0
        cam0-ov5647             Select OV5647 for camera on port 0
        cam0-ov64a40            Select OV64A40 for camera on port 0
        cam0-ov7251             Select OV7251 for camera on port 0
        cam0-ov9281             Select OV9281 for camera on port 0
        cam0-imx290-clk-freq    Set clock frequency for an IMX290 on port 0
        cam1-arducam-64mp       Select Arducam64MP for camera on port 1
        cam1-imx219             Select IMX219 for camera on port 1
        cam1-imx258             Select IMX258 for camera on port 1
        cam1-imx290             Select IMX290 for camera on port 1
        cam1-imx477             Select IMX477 for camera on port 1
        cam1-imx519             Select IMX519 for camera on port 1
        cam1-imx708             Select IMX708 for camera on port 1
        cam1-ov2311             Select OV2311 for camera on port 1
        cam1-ov5647             Select OV5647 for camera on port 1
        cam1-ov64a40            Select OV64A40 for camera on port 1
        cam1-ov7251             Select OV7251 for camera on port 1
        cam1-ov9281             Select OV9281 for camera on port 1
        cam1-imx290-clk-freq    Set clock frequency for an IMX290 on port 1
        cam2-arducam-64mp       Select Arducam64MP for camera on port 2
        cam2-imx219             Select IMX219 for camera on port 2
        cam2-imx258             Select IMX258 for camera on port 2
        cam2-imx290             Select IMX290 for camera on port 2
        cam2-imx477             Select IMX477 for camera on port 2
        cam2-imx519             Select IMX519 for camera on port 2
        cam2-imx708             Select IMX708 for camera on port 2
        cam2-ov2311             Select OV2311 for camera on port 2
        cam2-ov5647             Select OV5647 for camera on port 2
        cam2-ov64a40            Select OV64A40 for camera on port 2
        cam2-ov7251             Select OV7251 for camera on port 2
        cam2-ov9281             Select OV9281 for camera on port 2
        cam2-imx290-clk-freq    Set clock frequency for an IMX290 on port 2
        cam3-arducam-64mp       Select Arducam64MP for camera on port 3
        cam3-imx219             Select IMX219 for camera on port 3
        cam3-imx258             Select IMX258 for camera on port 3
        cam3-imx290             Select IMX290 for camera on port 3
        cam3-imx477             Select IMX477 for camera on port 3
        cam3-imx519             Select IMX519 for camera on port 3
        cam3-imx708             Select IMX708 for camera on port 3
        cam3-ov2311             Select OV2311 for camera on port 3
        cam3-ov5647             Select OV5647 for camera on port 3
        cam3-ov64a40            Select OV64A40 for camera on port 3
        cam3-ov7251             Select OV7251 for camera on port 3
        cam3-ov9281             Select OV9281 for camera on port 3
        cam3-imx290-clk-freq    Set clock frequency for an IMX290 on port 3
        cam0-sync-source        Set camera on port 0 as vsync source
        cam0-sync-sink          Set camera on port 0 as vsync sink
        cam1-sync-source        Set camera on port 1 as vsync source
        cam1-sync-sink          Set camera on port 1 as vsync sink
        cam2-sync-source        Set camera on port 2 as vsync source
        cam2-sync-sink          Set camera on port 2 as vsync sink
        cam3-sync-source        Set camera on port 3 as vsync source
        cam3-sync-sink          Set camera on port 3 as vsync sink

        cam0                    Connect the mux to CAM0 port (default is CAM1)


Name:   cap1106
Info:   Enables the ability to use the cap1106 touch sensor as a keyboard
Load:   dtoverlay=cap1106,<param>=<val>
Params: int_pin                 GPIO pin for interrupt signal (default 23)


Name:   chipcap2
Info:   Enables the Chipcap 2 humidity and temperature sensor. This device
        provides low and high humidity alarms and requires a 3V3 dedicated
        regulator to operate.
Load:   dtoverlay=chipcap2,<param>=<val>
Params: ready_pin               GPIO pin for ready signal (default 4)

        low_pin                 GPIO pin for low humidity signal (default 5)

        high_pin                GPIO pin for high humidity signal (default 6)

        reg_pin                 GPIO pin to control the dedicated regulator
                                that powers the device (default 26)


Name:   chipdip-dac
Info:   Configures Chip Dip audio cards.
Load:   dtoverlay=chipdip-dac
Params: <None>


Name:   cirrus-wm5102
Info:   Configures the Cirrus Logic Audio Card
Load:   dtoverlay=cirrus-wm5102
Params: <None>


Name:   cm-swap-i2c0
Info:   Largely for Compute Modules 1&3 where the original instructions for
        adding a camera used GPIOs 0&1 for CAM1 and 28&29 for CAM0, whilst all
        other platforms use 28&29 (or 44&45) for CAM1.
        The default assignment through using this overlay is for
        i2c0 to use 28&29, and i2c10 (aka i2c_csi_dsi) to use 28&29, but the
        overrides allow this to be changed.
Load:   dtoverlay=cm-swap-i2c0,<param>=<val>
Params: i2c0-gpio0              Use GPIOs 0&1 for i2c0
        i2c0-gpio28             Use GPIOs 28&29 for i2c0 (default)
        i2c0-gpio44             Use GPIOs 44&45 for i2c0
        i2c10-gpio0             Use GPIOs 0&1 for i2c0 (default)
        i2c10-gpio28            Use GPIOs 28&29 for i2c0
        i2c10-gpio44            Use GPIOs 44&45 for i2c0


Name:   cma
Info:   Set custom CMA sizes, only use if you know what you are doing, might
        clash with other overlays like vc4-fkms-v3d and vc4-kms-v3d.
Load:   dtoverlay=cma,<param>=<val>
Params: cma-512                 CMA is 512MB (needs 1GB)
        cma-448                 CMA is 448MB (needs 1GB)
        cma-384                 CMA is 384MB (needs 1GB)
        cma-320                 CMA is 320MB (needs 1GB)
        cma-256                 CMA is 256MB (needs 1GB)
        cma-192                 CMA is 192MB (needs 1GB)
        cma-128                 CMA is 128MB
        cma-96                  CMA is 96MB
        cma-64                  CMA is 64MB
        cma-size                CMA size in bytes, 4MB aligned
        cma-default             Use upstream's default value


Name:   crystalfontz-cfa050_pi_m
Info:   Configures the Crystalfontz CFA050-PI-M series of Raspberry Pi CM4
        based modules using the CFA7201280A0_050Tx 7" TFT LCD displays,
        with or without capacitive touch screen.
        Requires use of vc4-kms-v3d.
Load:   dtoverlay=crystalfontz-cfa050_pi_m,<param>=<val>
Params: captouch                Enable capacitive touch display


Name:   cutiepi-panel
Info:   8" TFT LCD display and touch panel used by cutiepi.io
Load:   dtoverlay=cutiepi-panel
Params: <None>


Name:   dacberry400
Info:   Configures the dacberry400 add on soundcard
Load:   dtoverlay=dacberry400
Params: <None>


Name:   dht11
Info:   Overlay for the DHT11/DHT21/DHT22 humidity/temperature sensors
        Also sometimes found with the part number(s) AM230x.
Load:   dtoverlay=dht11,<param>=<val>
Params: gpiopin                 GPIO connected to the sensor's DATA output.
                                (default 4)


Name:   dionaudio-kiwi
Info:   Configures the Dion Audio KIWI STREAMER
Load:   dtoverlay=dionaudio-kiwi
Params: <None>


Name:   dionaudio-loco
Info:   Configures the Dion Audio LOCO DAC-AMP
Load:   dtoverlay=dionaudio-loco
Params: <None>


Name:   dionaudio-loco-v2
Info:   Configures the Dion Audio LOCO-V2 DAC-AMP
Load:   dtoverlay=dionaudio-loco-v2,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   disable-bt
Info:   Disable onboard Bluetooth on Bluetooth-capable Raspberry Pis. On Pis
        prior to Pi 5 this restores UART0/ttyAMA0 over GPIOs 14 & 15.
Load:   dtoverlay=disable-bt
Params: <None>


Name:   disable-bt-pi5
Info:   See disable-bt


Name:   disable-emmc2
Info:   Disable EMMC2 controller on BCM2711.
        The allows the onboard EMMC storage on Compute Module 4 to be disabled
        e.g. if a fault has occurred.
Load:   dtoverlay=disable-emmc2
Params: <None>


Name:   disable-wifi
Info:   Disable onboard WLAN on WiFi-capable Raspberry Pis.
Load:   dtoverlay=disable-wifi
Params: <None>


Name:   disable-wifi-pi5
Info:   See disable-wifi


Name:   dpi18
Info:   Overlay for a generic 18-bit DPI display
        This uses GPIOs 0-21 (so no I2C, uart etc.), and activates the output
        2-3 seconds after the kernel has started.
Load:   dtoverlay=dpi18
Params: <None>


Name:   dpi18cpadhi
Info:   Overlay for a generic 18-bit DPI display (in 'mode 6' connection scheme)
        This uses GPIOs 0-9,12-17,20-25 (so no I2C, uart etc.), and activates
        the output 3-3 seconds after the kernel has started.
Load:   dtoverlay=dpi18cpadhi
Params: <None>


Name:   dpi24
Info:   Overlay for a generic 24-bit DPI display
        This uses GPIOs 0-27 (so no I2C, uart etc.), and activates the output
        2-3 seconds after the kernel has started.
Load:   dtoverlay=dpi24
Params: <None>


Name:   draws
Info:   Configures the NW Digital Radio DRAWS Hat

        The board includes an ADC to measure various board values and also
        provides two analog user inputs on the expansion header.  The ADC
        can be configured for various sample rates and gain values to adjust
        the input range.  Tables describing the two parameters follow.

        ADC Gain Values:
            0 = +/- 6.144V
            1 = +/- 4.096V
            2 = +/- 2.048V
            3 = +/- 1.024V
            4 = +/- 0.512V
            5 = +/- 0.256V
            6 = +/- 0.256V
            7 = +/- 0.256V

        ADC Datarate Values:
            0 = 128sps
            1 = 250sps
            2 = 490sps
            3 = 920sps
            4 = 1600sps (default)
            5 = 2400sps
            6 = 3300sps
            7 = 3300sps
Load:   dtoverlay=draws,<param>=<val>
Params: draws_adc_ch4_gain      Sets the full scale resolution of the ADCs
                                input voltage sensor (default 1)

        draws_adc_ch4_datarate  Sets the datarate of the ADCs input voltage
                                sensor

        draws_adc_ch5_gain      Sets the full scale resolution of the ADCs
                                5V rail voltage sensor (default 1)

        draws_adc_ch5_datarate  Sets the datarate of the ADCs 4V rail voltage
                                sensor

        draws_adc_ch6_gain      Sets the full scale resolution of the ADCs
                                AIN2 input (default 2)

        draws_adc_ch6_datarate  Sets the datarate of the ADCs AIN2 input

        draws_adc_ch7_gain      Sets the full scale resolution of the ADCs
                                AIN3 input (default 2)

        draws_adc_ch7_datarate  Sets the datarate of the ADCs AIN3 input

        alsaname                Name of the ALSA audio device (default "draws")


Name:   dwc-otg
Info:   Selects the dwc_otg USB controller driver which has fiq support. This
        is the default on all except the Pi Zero which defaults to dwc2.
Load:   dtoverlay=dwc-otg
Params: <None>


Name:   dwc2
Info:   Selects the dwc2 USB controller driver
Load:   dtoverlay=dwc2,<param>=<val>
Params: dr_mode                 Dual role mode: "host", "peripheral" or "otg"

        g-rx-fifo-size          Size of rx fifo size in gadget mode

        g-np-tx-fifo-size       Size of non-periodic tx fifo size in gadget
                                mode


[ The ds1307-rtc overlay has been deleted. See i2c-rtc. ]


Name:   edt-ft5406
Info:   Overlay for the EDT FT5406 touchscreen.
        This works with the Raspberry Pi 7" touchscreen when not being polled
        by the firmware.
        By default the overlay uses the i2c_csi_dsi I2C interface, but this
        can be overridden
        You MUST use either "disable_touchscreen=1" or "ignore_lcd=1" in
        config.txt to stop the firmware polling the touchscreen.
Load:   dtoverlay=edt-ft5406,<param>=<val>
Params: sizex                   Touchscreen size x (default 800)
        sizey                   Touchscreen size y (default 480)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        addr                    Sets the address for the touch controller. Note
                                that the device must be configured to use the
                                specified address.
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"


Name:   enc28j60
Info:   Overlay for the Microchip ENC28J60 Ethernet Controller on SPI0
Load:   dtoverlay=enc28j60,<param>=<val>
Params: int_pin                 GPIO used for INT (default 25)

        speed                   SPI bus speed (default 12000000)


Name:   enc28j60-spi2
Info:   Overlay for the Microchip ENC28J60 Ethernet Controller on SPI2
Load:   dtoverlay=enc28j60-spi2,<param>=<val>
Params: int_pin                 GPIO used for INT (default 39)

        speed                   SPI bus speed (default 12000000)


Name:   exc3000
Info:   Enables I2C connected EETI EXC3000 multiple touch controller using
        GPIO 4 (pin 7 on GPIO header) for interrupt.
Load:   dtoverlay=exc3000,<param>=<val>
Params: interrupt               GPIO used for interrupt (default 4)
        sizex                   Touchscreen size x (default 4096)
        sizey                   Touchscreen size y (default 4096)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis


Name:   ezsound-6x8iso
Info:   Overlay for the ezsound 6x8 isolated soundcard.
Load:   dtoverlay=ezsound-6x8iso
Params: <None>


Name:   fbtft
Info:   Overlay for SPI-connected displays using the fbtft drivers.

        This overlay seeks to replace the functionality provided by fbtft_device
        which is now gone from the kernel.

        Most displays from fbtft_device have been ported over.
        Example:
          dtoverlay=fbtft,spi0-0,rpi-display,reset_pin=23,dc_pin=24,led_pin=18,rotate=270

        It is also possible to specify the controller (this will use the default
        init sequence in the driver).
        Example:
          dtoverlay=fbtft,spi0-0,ili9341,bgr,reset_pin=23,dc_pin=24,led_pin=18,rotate=270

        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.

        The following features of fbtft_device have not been ported over:
        - parallel bus is not supported
        - the init property which overrides the controller initialization
          sequence is not supported as a parameter due to memory limitations in
          the bootloader responsible for applying the overlay.

        See https://github.com/notro/fbtft/wiki/FBTFT-RPI-overlays for how to
        create an overlay.

Load:   dtoverlay=fbtft,<param>=<val>
Params:
        spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)
        speed                   SPI bus speed in Hz (default 32000000)
        cpha                    Shifted clock phase (CPHA) mode
        cpol                    Inverse clock polarity (CPOL) mode

        adafruit18              Adafruit 1.8
        adafruit22              Adafruit 2.2 (old)
        adafruit22a             Adafruit 2.2
        adafruit28              Adafruit 2.8
        adafruit13m             Adafruit 1.3 OLED
        admatec_c-berry28       C-Berry28
        dogs102                 EA DOGS102
        er_tftm050_2            ER-TFTM070-2
        er_tftm070_5            ER-TFTM070-5
        ew24ha0                 EW24HA0
        ew24ha0_9bit            EW24HA0 in 9-bit mode
        freetronicsoled128      Freetronics OLED128
        hy28a                   HY28A
        hy28b                   HY28B
        itdb28_spi              ITDB02-2.8 with SPI interface circuit
        mi0283qt-2              Watterott MI0283QT-2
        mi0283qt-9a             Watterott MI0283QT-9A
        nokia3310               Nokia 3310
        nokia3310a              Nokia 3310a
        nokia5110               Nokia 5110
        piscreen                PiScreen
        pitft                   Adafruit PiTFT 2.8
        pioled                  ILSoft OLED
        rpi-display             Watterott rpi-display
        sainsmart18             Sainsmart 1.8
        sainsmart32_spi         Sainsmart 3.2 with SPI interfce circuit
        tinylcd35               TinyLCD 3.5
        tm022hdh26              Tianma TM022HDH26
        tontec35_9481           Tontect 3.5 with ILI9481 controller
        tontec35_9486           Tontect 3.5 with ILI9486 controller
        waveshare32b            Waveshare 3.2
        waveshare22             Waveshare 2.2

        bd663474                BD663474 display controller
        hx8340bn                HX8340BN display controller
        hx8347d                 HX8347D display controller
        hx8353d                 HX8353D display controller
        hx8357d                 HX8357D display controller
        ili9163                 ILI9163 display controller
        ili9320                 ILI9320 display controller
        ili9325                 ILI9325 display controller
        ili9340                 ILI9340 display controller
        ili9341                 ILI9341 display controller
        ili9481                 ILI9481 display controller
        ili9486                 ILI9486 display controller
        pcd8544                 PCD8544 display controller
        ra8875                  RA8875 display controller
        s6d02a1                 S6D02A1 display controller
        s6d1121                 S6D1121 display controller
        seps525                 SEPS525 display controller
        sh1106                  SH1106 display controller
        ssd1289                 SSD1289 display controller
        ssd1305                 SSD1305 display controller
        ssd1306                 SSD1306 display controller
        ssd1325                 SSD1325 display controller
        ssd1331                 SSD1331 display controller
        ssd1351                 SSD1351 display controller
        st7735r                 ST7735R display controller
        st7789v                 ST7789V display controller
        tls8204                 TLS8204 display controller
        uc1611                  UC1611 display controller
        uc1701                  UC1701 display controller
        upd161704               UPD161704 display controller

        width                   Display width in pixels
        height                  Display height in pixels
        regwidth                Display controller register width (default is
                                driver specific)
        buswidth                Display bus interface width (default 8)
        debug                   Debug output level {0-7}
        rotate                  Display rotation {0, 90, 180, 270} (counter
                                clockwise). Not supported by all drivers.
        bgr                     Enable BGR mode (default off). Use if Red and
                                Blue are swapped. Not supported by all drivers.
        fps                     Frames per second (default 30). In effect this
                                states how long the driver will wait after video
                                memory has been changed until display update
                                transfer is started.
        txbuflen                Length of the FBTFT transmit buffer
                                (default 4096)
        startbyte               Sets the Start byte used by fb_ili9320,
                                fb_ili9325 and fb_hx8347d. Common value is 0x70.
        gamma                   String representation of Gamma Curve(s). Driver
                                specific. Not supported by all drivers.
        reset_pin               GPIO pin for RESET
        dc_pin                  GPIO pin for D/C
        led_pin                 GPIO pin for LED backlight


Name:   fe-pi-audio
Info:   Configures the Fe-Pi Audio Sound Card
Load:   dtoverlay=fe-pi-audio
Params: <None>


Name:   fsm-demo
Info:   A demonstration of the gpio-fsm driver. The GPIOs are chosen to work
        nicely with a "traffic-light" display of red, amber and green LEDs on
        GPIOs 7, 8 and 25 respectively.
Load:   dtoverlay=fsm-demo,<param>=<val>
Params: fsm_debug               Enable debug logging (default off)


Name:   gc9a01
Info:   Enables GalaxyCore's GC9A01 single chip driver based displays on
        SPI0 as fb1, using GPIOs DC=25, RST=27 and BL=18 (physical
        GPIO header pins 22, 13 and 12 respectively) in addition to the
        SPI0 pins DIN=10, CLK=11 and CS=8 (physical GPIO header pins 19,
        23 and 24 respectively).
Load:   dtoverlay=gc9a01,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        width                   Width of the display

        height                  Height of the display

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}


Name:   ghost-amp
Info:   An overlay for the Ghost amplifier.
Load:   dtoverlay=ghost-amp,<param>=<val>
Params: fsm_debug               Enable debug logging of the GPIO FSM (default
                                off)


Name:   goodix
Info:   Enables I2C connected Goodix gt9271 multiple touch controller using
        GPIOs 4 and 17 (pins 7 and 11 on GPIO header) for interrupt and reset.
Load:   dtoverlay=goodix,<param>=<val>
Params: addr                    I2C address (default 0x14)
        interrupt               GPIO used for interrupt (default 4)
        reset                   GPIO used for reset (default 17)
        i2c-path                Override I2C path to allow for i2c-gpio buses


Name:   googlevoicehat-soundcard
Info:   Configures the Google voiceHAT soundcard
Load:   dtoverlay=googlevoicehat-soundcard
Params: <None>


Name:   gpio-charger
Info:   This is a generic overlay for detecting charger with GPIO.
Load:   dtoverlay=gpio-charger,<param>=<val>
Params: gpio                    GPIO pin to trigger on (default 4)
        active_low              When this is 1 (active low), a falling
                                edge generates a charging event and a
                                rising edge generates a discharging event.
                                When this is 0 (active high), this is
                                reversed. The default is 0 (active high)
        gpio_pull               Desired pull-up/down state (off, down, up)
                                Default is "down".
        type                    Set a charger type for the pin. (Default: mains)


Name:   gpio-fan
Info:   Configure a GPIO pin to control a cooling fan.
Load:   dtoverlay=gpio-fan,<param>=<val>
Params: gpiopin                 GPIO used to control the fan (default 12)
        temp                    Temperature at which the fan switches on, in
                                millicelcius (default 55000)
        hyst                    Temperature delta (in millicelcius) below
                                temp at which the fan will drop to minrpm
                                (default 10000)


Name:   gpio-hog
Info:   Activate a "hog" for a GPIO - request that the kernel configures it as
        an output, driven low or high as indicated by the presence or absence
        of the active_low parameter. Note that a hogged GPIO is not available
        to other drivers or for gpioset/gpioget.
Load:   dtoverlay=gpio-hog,<param>=<val>
Params: gpio                    GPIO pin to hog (default 26)
        active_low              If set, the hog drives the GPIO low (defaults
                                to off - the GPIO is driven high)


Name:   gpio-ir
Info:   Use GPIO pin as rc-core style infrared receiver input. The rc-core-
        based gpio_ir_recv driver maps received keys directly to a
        /dev/input/event* device, all decoding is done by the kernel - LIRC is
        not required! The key mapping and other decoding parameters can be
        configured by "ir-keytable" tool.
Load:   dtoverlay=gpio-ir,<param>=<val>
Params: gpio_pin                Input pin number. Default is 18.

        gpio_pull               Desired pull-up/down state (off, down, up)
                                Default is "up".

        invert                  "1" = invert the input (active-low signalling).
                                "0" = non-inverted input (active-high
                                signalling). Default is "1".

        rc-map-name             Default rc keymap (can also be changed by
                                ir-keytable), defaults to "rc-rc6-mce"


Name:   gpio-ir-tx
Info:   Use GPIO pin as bit-banged infrared transmitter output.
        This is an alternative to "pwm-ir-tx". gpio-ir-tx doesn't require
        a PWM so it can be used together with onboard analog audio.
Load:   dtoverlay=gpio-ir-tx,<param>=<val>
Params: gpio_pin                Output GPIO (default 18)

        invert                  "1" = invert the output (make it active-low).
                                Default is "0" (active-high).


Name:   gpio-key
Info:   This is a generic overlay for activating GPIO keypresses using
        the gpio-keys library and this dtoverlay. Multiple keys can be
        set up using multiple calls to the overlay for configuring
        additional buttons or joysticks. You can see available keycodes
        at https://github.com/torvalds/linux/blob/v4.12/include/uapi/
        linux/input-event-codes.h#L64
Load:   dtoverlay=gpio-key,<param>=<val>
Params: gpio                    GPIO pin to trigger on (default 3)
        active_low              When this is 1 (active low), a falling
                                edge generates a key down event and a
                                rising edge generates a key up event.
                                When this is 0 (active high), this is
                                reversed. The default is 1 (active low)
        gpio_pull               Desired pull-up/down state (off, down, up)
                                Default is "up". Note that the default pin
                                (GPIO3) has an external pullup
        label                   Set a label for the key
        keycode                 Set the key code for the button



Name:   gpio-led
Info:   This is a generic overlay for activating LEDs (or any other component)
        by a GPIO pin. Multiple LEDs can be set up using multiple calls to the
        overlay. While there are many existing methods to activate LEDs on the
        RPi, this method offers some advantages:
        1) Does not require any userspace programs.
        2) LEDs can be connected to the kernel's led-trigger framework,
           and drive the LED based on triggers such as cpu load, heartbeat,
           kernel panic, key input, timers and others.
        3) LED can be tied to the input state of another GPIO pin.
        4) The LED is setup early during the kernel boot process (useful
           for cpu/heartbeat/panic triggers).

        Typical electrical connection is:
           RPI-GPIO.19  ->  LED  -> 300ohm resister  -> RPI-GND
        The GPIO pin number can be changed with the 'gpio=' parameter.

        To control an LED from userspace, write a 0 or 1 value:
           echo 1 > /sys/class/leds/myled1/brightness
        The 'myled1' name can be changed with the 'label=' parameter.

        To connect the LED to a kernel trigger from userspace:
           echo cpu > /sys/class/leds/myled1/trigger
           echo heartbeat > /sys/class/leds/myled1/trigger
           echo none > /sys/class/leds/myled1/trigger
        To connect the LED to GPIO.26 pin (physical pin 37):
           echo gpio > /sys/class/leds/myled1/trigger
           echo 26 > /sys/class/leds/myled1/gpio
        Available triggers:
           cat /sys/class/leds/myled1/trigger

        More information about the Linux kernel LED/Trigger system:
           https://www.kernel.org/doc/Documentation/leds/leds-class.rst
           https://www.kernel.org/doc/Documentation/leds/ledtrig-oneshot.rst
Load:   dtoverlay=gpio-led,<param>=<val>
Params: gpio                    GPIO pin connected to the LED (default 19)
        label                   The label for this LED. It will appear under
                                /sys/class/leds/<label> . Default 'myled1'.
        trigger                 Set the led-trigger to connect to this LED.
                                default 'none' (LED is user-controlled).
                                Some possible triggers:
                                 cpu - CPU load (all CPUs)
                                 cpu0 - CPU load of first CPU.
                                 mmc - disk activity (all disks)
                                 panic - turn on on kernel panic
                                 heartbeat - indicate system health
                                 gpio - connect to a GPIO input pin (note:
                                        currently the GPIO PIN can not be set
                                        using overlay parameters, must be
                                        done in userspace, see examples above.
        active_low              Set to 1 to turn invert the LED control
                                (writing 0 to /sys/class/leds/XXX/brightness
                                will turn on the GPIO/LED). Default '0'.


Name:   gpio-no-bank0-irq
Info:   Use this overlay to disable GPIO interrupts for GPIOs in bank 0 (0-27),
        which can be useful for UIO drivers.
        N.B. Using this overlay will trigger a kernel WARN during booting, but
        this can safely be ignored - the system should work as expected.
Load:   dtoverlay=gpio-no-bank0-irq
Params: <None>


Name:   gpio-no-irq
Info:   Use this overlay to disable all GPIO interrupts, which can be useful
        for user-space GPIO edge detection systems.
Load:   dtoverlay=gpio-no-irq
Params: <None>


Name:   gpio-poweroff
Info:   Drives a GPIO high or low on poweroff (including halt). Using this
        overlay interferes with the normal power-down sequence, preventing the
        kernel from resetting the SoC (a necessary step in a normal power-off
        or reboot). This also disables the ability to trigger a boot by driving
        GPIO3 low.

        The GPIO starts in an inactive state. At poweroff time it is driven
        active for 100ms, then inactive for 100ms, then active again. It is
        safe to remove the power at any point after the initial activation of
        the GPIO.

        Users of this overlay are required to provide an external mechanism to
        switch off the power supply when signalled - failure to do so results
        in a kernel BUG, increased power consumption and undefined behaviour.
Load:   dtoverlay=gpio-poweroff,<param>=<val>
Params: gpiopin                 GPIO for signalling (default 26)

        active_low              Set if the power control device requires a
                                high->low transition to trigger a power-down.
                                Note that this will require the support of a
                                custom dt-blob.bin to prevent a power-down
                                during the boot process, and that a reboot
                                will also cause the pin to go low.
        input                   Set if the gpio pin should be configured as
                                an input.
        export                  Set to export the configured pin to sysfs
        active_delay_ms         Initial GPIO active period (default 100)
        inactive_delay_ms       Subsequent GPIO inactive period (default 100)
        timeout_ms              Specify (in ms) how long the kernel waits for
                                power-down before issuing a WARN (default 3000).


Name:   gpio-shutdown
Info:   Initiates a shutdown when GPIO pin changes. The given GPIO pin
        is configured as an input key that generates KEY_POWER events.

        This event is handled by systemd-logind by initiating a
        shutdown. Systemd versions older than 225 need an udev rule
        enable listening to the input device:

                ACTION!="REMOVE", SUBSYSTEM=="input", KERNEL=="event*", \
                        SUBSYSTEMS=="platform", DRIVERS=="gpio-keys", \
                        ATTRS{keys}=="116", TAG+="power-switch"

        Alternatively this event can be handled also on systems without
        systemd, just by traditional SysV init daemon. KEY_POWER event
        (keycode 116) needs to be mapped to KeyboardSignal on console
        and then kb::kbrequest inittab action which is triggered by
        KeyboardSignal from console can be configured to issue system
        shutdown. Steps for this configuration are:

            Add following lines to the /etc/console-setup/remap.inc file:

                # Key Power as special keypress
                keycode 116 = KeyboardSignal

            Then add following lines to /etc/inittab file:

                # Action on special keypress (Key Power)
                kb::kbrequest:/sbin/shutdown -t1 -a -h -P now

            And finally reload configuration by calling following commands:

                # dpkg-reconfigure console-setup
                # service console-setup reload
                # init q

        This overlay only handles shutdown. After shutdown, the system
        can be powered up again by driving GPIO3 low. The default
        configuration uses GPIO3 with a pullup, so if you connect a
        button between GPIO3 and GND (pin 5 and 6 on the 40-pin header),
        you get a shutdown and power-up button. Please note that
        Raspberry Pi 1 Model B rev 1 uses GPIO1 instead of GPIO3.
Load:   dtoverlay=gpio-shutdown,<param>=<val>
Params: gpio_pin                GPIO pin to trigger on (default 3)
                                For Raspberry Pi 1 Model B rev 1 set this
                                explicitly to value 1, e.g.:

                                    dtoverlay=gpio-shutdown,gpio_pin=1

        active_low              When this is 1 (active low), a falling
                                edge generates a key down event and a
                                rising edge generates a key up event.
                                When this is 0 (active high), this is
                                reversed. The default is 1 (active low).

        gpio_pull               Desired pull-up/down state (off, down, up)
                                Default is "up".

                                Note that the default pin (GPIO3) has an
                                external pullup. Same applies for GPIO1
                                on Raspberry Pi 1 Model B rev 1.

        debounce                Specify the debounce interval in milliseconds
                                (default 100)


Name:   hd44780-i2c-lcd
Info:   Configures an HD44780 compatible LCD display connected via a PCF8574 as
        is often found as a backpack interface for these displays.
Load:   dtoverlay=hd44780-i2c-lcd,<param>=<val>
Params: addr                    I2C address of PCF8574
        pin_d4                  GPIO pin for data pin D4 (default 4)

        pin_d5                  GPIO pin for data pin D5 (default 5)

        pin_d6                  GPIO pin for data pin D6 (default 6)

        pin_d7                  GPIO pin for data pin D7 (default 7)

        pin_en                  GPIO pin for "Enable" (default 2)

        pin_rs                  GPIO pin for "Register Select" (default 0)

        pin_rw                  GPIO pin for R/W select (default 1)

        pin_bl                  GPIO pin for enabling/disabling the display
                                backlight. (default 3)

        display_height          Height of the display in characters (default 2)

        display_width           Width of the display in characters (default 16)
        i2c-path                Override I2C path to allow for i2c-gpio buses


Name:   hd44780-lcd
Info:   Configures an HD44780 compatible LCD display. Uses 4 gpio pins for
        data, 2 gpio pins for enable and register select and 1 optional pin
        for enabling/disabling the backlight display.
Load:   dtoverlay=hd44780-lcd,<param>=<val>
Params: pin_d4                  GPIO pin for data pin D4 (default 6)

        pin_d5                  GPIO pin for data pin D5 (default 13)

        pin_d6                  GPIO pin for data pin D6 (default 19)

        pin_d7                  GPIO pin for data pin D7 (default 26)

        pin_en                  GPIO pin for "Enable" (default 21)

        pin_rs                  GPIO pin for "Register Select" (default 20)

        pin_bl                  Optional pin for enabling/disabling the
                                display backlight. (default disabled)

        display_height          Height of the display in characters (default 2)

        display_width           Width of the display in characters (default 16)


Name:   hdmi-backlight-hwhack-gpio
Info:   Devicetree overlay for GPIO based backlight on/off capability.
        Use this if you have one of those HDMI displays whose backlight cannot
        be controlled via DPMS over HDMI and plan to do a little soldering to
        use an RPi gpio pin for on/off switching. See:
        https://www.waveshare.com/wiki/7inch_HDMI_LCD_(C)#Backlight_Control
Load:   dtoverlay=hdmi-backlight-hwhack-gpio,<param>=<val>
Params: gpio_pin                GPIO pin used (default 17)
        active_low              Set this to 1 if the display backlight is
                                switched on when the wire goes low.
                                Leave the default (value 0) if the backlight
                                expects a high to switch it on.


Name:   hifiberry-adc
Info:   Configures the HifiBerry ADC audio card
Load:   dtoverlay=hifiberry-adc,<param>=<val>
Params: leds_off                If set to 'true' the onboard indicator LED
                                is switched off at all times.


Name:   hifiberry-adc8x
Info:   Configures the HifiBerry ADC8X audio card (only on Pi5)
Load:   dtoverlay=hifiberry-adc8x
Params: <None>


Name:   hifiberry-amp
Info:   Configures the HifiBerry Amp and Amp+ audio cards
Load:   dtoverlay=hifiberry-amp
Params: <None>


Name:   hifiberry-amp100
Info:   Configures the HifiBerry AMP100 audio card
Load:   dtoverlay=hifiberry-amp100,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-amp100,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        slave                   Force AMP100 into slave mode, using Pi as
                                master for bit clock and frame clock.
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.
        auto_mute               If set to 'true' the amplifier is automatically
                                muted when the DAC is not playing.
        mute_ext_ctl            The amplifier's HW mute control is enabled
                                in ALSA mixer and set to <val>.
                                Will be overwritten by ALSA user settings.


Name:   hifiberry-amp3
Info:   Configures the HifiBerry Amp3 audio card
Load:   dtoverlay=hifiberry-amp3
Params: <None>


Name:   hifiberry-amp4pro
Info:   Configures the HifiBerry AMP4 Pro audio card
Load:   dtoverlay=hifiberry-amp4pro,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the TAS5756
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-amp4pro,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        slave                   Force the amp into slave mode, using Pi as
                                master for bit clock and frame clock.
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.
        auto_mute               If set to 'true' the amplifier is automatically
                                muted when it is not playing.
        mute_ext_ctl            The amplifier's HW mute control is enabled
                                in ALSA mixer and set to <val>.
                                Will be overwritten by ALSA user settings.


Name:   hifiberry-dac
Info:   Configures the HifiBerry DAC audio cards
Load:   dtoverlay=hifiberry-dac
Params: <None>


Name:   hifiberry-dac8x
Info:   Configures the HifiBerry DAC8X audio cards (only on Pi5)
        This driver also detects a stacked ADC8x and activates the
        capture capabilities.
        Note: for standalone use of the ADC8x activate the ADC8x module.
Load:   dtoverlay=hifiberry-dac8x
Params: <None>


Name:   hifiberry-dacplus
Info:   Configures the HifiBerry DAC+ audio card
Load:   dtoverlay=hifiberry-dacplus,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        slave                   Force DAC+ into slave mode, using Pi as
                                master for bit clock and frame clock.
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.


Name:   hifiberry-dacplus-pro
Info:   Configures the HifiBerry DAC+ PRO audio card (onboard clocks)
Load:   dtoverlay=hifiberry-dacplus-pro,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.


Name:   hifiberry-dacplus-std
Info:   Configures the HifiBerry DAC+ standard audio card (no onboard clocks)
Load:   dtoverlay=hifiberry-dacplus-std,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.


Name:   hifiberry-dacplusadc
Info:   Configures the HifiBerry DAC+ADC audio card
Load:   dtoverlay=hifiberry-dacplusadc,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.


Name:   hifiberry-dacplusadcpro
Info:   Configures the HifiBerry DAC+ADC PRO audio card
Load:   dtoverlay=hifiberry-dacplusadcpro,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=hifiberry-dacplusadcpro,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        slave                   Force DAC+ADC Pro into slave mode, using Pi as
                                master for bit clock and frame clock.
        leds_off                If set to 'true' the onboard indicator LEDs
                                are switched off at all times.


Name:   hifiberry-dacplusdsp
Info:   Configures the HifiBerry DAC+DSP audio card
Load:   dtoverlay=hifiberry-dacplusdsp
Params: <None>


Name:   hifiberry-dacplushd
Info:   Configures the HifiBerry DAC+ HD audio card
Load:   dtoverlay=hifiberry-dacplushd
Params: <None>


Name:   hifiberry-digi
Info:   Configures the HifiBerry Digi and Digi+ audio card
Load:   dtoverlay=hifiberry-digi
Params: <None>


Name:   hifiberry-digi-pro
Info:   Configures the HifiBerry Digi+ Pro and Digi2 Pro audio card
Load:   dtoverlay=hifiberry-digi-pro
Params: <None>


Name:   highperi
Info:   Enables "High Peripheral" mode
Load:   dtoverlay=highperi
Params: <None>


Name:   hy28a
Info:   HY28A - 2.8" TFT LCD Display Module by HAOYU Electronics
        Default values match Texy's display shield
Load:   dtoverlay=hy28a,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)

        resetgpio               GPIO used to reset controller

        ledgpio                 GPIO used to control backlight


Name:   hy28b
Info:   HY28B - 2.8" TFT LCD Display Module by HAOYU Electronics
        Default values match Texy's display shield
Load:   dtoverlay=hy28b,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)

        resetgpio               GPIO used to reset controller

        ledgpio                 GPIO used to control backlight


Name:   hy28b-2017
Info:   HY28B 2017 version - 2.8" TFT LCD Display Module by HAOYU Electronics
        Default values match Texy's display shield
Load:   dtoverlay=hy28b-2017,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)

        resetgpio               GPIO used to reset controller

        ledgpio                 GPIO used to control backlight


Name:   i-sabre-q2m
Info:   Configures the Audiophonics I-SABRE Q2M DAC
Load:   dtoverlay=i-sabre-q2m
Params: <None>


Name:   i2c-bcm2708
Info:   Fall back to the i2c_bcm2708 driver for the i2c_arm bus.
Load:   dtoverlay=i2c-bcm2708
Params: <None>


Name:   i2c-bus
Info:   This is not a real overlay. Many overlays support the use of a variety
        of I2C buses, and this is where the relevant parameters are documented.
Load:   <Documentation>
Params: i2c0                    Choose the I2C0 bus on GPIOs 0&1
        i2c1                    Choose the I2C1 bus on GPIOs 2&3
        i2c2                    Choose the I2C2 bus (configure with the i2c2
                                overlay - BCM2711 only)
        i2c3                    Choose the I2C3 bus (configure with the i2c3
                                overlay - BCM2711 only)
        i2c4                    Choose the I2C4 bus (configure with the i2c4
                                overlay - BCM2711 only)
        i2c5                    Choose the I2C5 bus (configure with the i2c5
                                overlay - BCM2711 only)
        i2c6                    Choose the I2C6 bus (configure with the i2c6
                                overlay - BCM2711 only)
        i2c_csi_dsi             Choose the I2C bus connected to the main
                                camera/display connector.
                                See "dtparam -h i2c_csi_dsi" for details.
        i2c_csi_dsi0            Choose the I2C bus connected to the second
                                camera/display connector, if present.
                                See "dtparam -h i2c_csi_dsi0" for details.
        i2c-path                Override I2C path to allow for i2c-gpio buses


Name:   i2c-fan
Info:   Adds support for a number of I2C fan controllers
Load:   dtoverlay=i2c-fan,<param>=<val>
Params: addr                    Sets the address for the fan controller. Note
                                that the device must be configured to use the
                                specified address.

        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"

        minpwm                  PWM setting for the fan when the SoC is below
                                mintemp (range 0-255. default 0)
        maxpwm                  PWM setting for the fan when the SoC is above
                                maxtemp (range 0-255. default 255)
        midtemp                 Temperature (in millicelcius) at which the fan
                                begins to speed up (default 50000)

        midtemp_hyst            Temperature delta (in millicelcius) below
                                mintemp at which the fan will drop to minrpm
                                (default 2000)

        maxtemp                 Temperature (in millicelcius) at which the fan
                                will be held at maxrpm (default 70000)

        maxtemp_hyst            Temperature delta (in millicelcius) below
                                maxtemp at which the fan begins to slow down
                                (default 2000)

        emc2301                 Select the Microchip EMC230x controller family
                                - EMC2301, EMC2302, EMC2303, EMC2305.


Name:   i2c-gpio
Info:   Adds support for software i2c controller on gpio pins
Load:   dtoverlay=i2c-gpio,<param>=<val>
Params: i2c_gpio_sda            GPIO used for I2C data (default "23")

        i2c_gpio_scl            GPIO used for I2C clock (default "24")

        i2c_gpio_delay_us       Clock delay in microseconds
                                (default "2" = ~100kHz)

        bus                     Set to a unique, non-zero value if wanting
                                multiple i2c-gpio busses. If set, will be used
                                as the preferred bus number (/dev/i2c-<n>). If
                                not set, the default value is 0, but the bus
                                number will be dynamically assigned - probably
                                3.


Name:   i2c-mux
Info:   Adds support for a number of I2C bus multiplexers on i2c_arm
Load:   dtoverlay=i2c-mux,<param>=<val>
Params: pca9542                 Select the NXP PCA9542 device

        pca9545                 Select the NXP PCA9545 device

        pca9548                 Select the NXP PCA9548 device

        addr                    Change I2C address of the device (default 0x70)

        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"

        base                    Set an explicit base value for the channel bus
                                numbers

        disconnect_on_idle      Force the mux to disconnect all child buses
                                after every transaction.


[ The i2c-mux-pca9548a overlay has been deleted. See i2c-mux. ]


Name:   i2c-pwm-pca9685a
Info:   Adds support for an NXP PCA9685A I2C PWM controller on i2c_arm
Load:   dtoverlay=i2c-pwm-pca9685a,<param>=<val>
Params: addr                    I2C address of PCA9685A (default 0x40)
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"


Name:   i2c-rtc
Info:   Adds support for a number of I2C Real Time Clock devices
Load:   dtoverlay=i2c-rtc,<param>=<val>
Params: abx80x                  Select one of the ABx80x family:
                                  AB0801, AB0803, AB0804, AB0805,
                                  AB1801, AB1803, AB1804, AB1805

        bq32000                 Select the TI BQ32000 device

        ds1307                  Select the DS1307 device

        ds1339                  Select the DS1339 device

        ds1340                  Select the DS1340 device

        ds3231                  Select the DS3231 device

        m41t62                  Select the M41T62 device

        mcp7940x                Select the MCP7940x device

        mcp7941x                Select the MCP7941x device

        pcf2127                 Select the PCF2127 device

        pcf2129                 Select the PCF2129 device

        pcf2131                 Select the PCF2131 device

        pcf85063                Select the PCF85063 device

        pcf85063a               Select the PCF85063A device

        pcf8523                 Select the PCF8523 device

        pcf85363                Select the PCF85363 device

        pcf8563                 Select the PCF8563 device

        rv1805                  Select the Micro Crystal RV1805 device

        rv3028                  Select the Micro Crystal RV3028 device

        rv3032                  Select the Micro Crystal RV3032 device

        rv8803                  Select the Micro Crystal RV8803 device

        sd3078                  Select the ZXW Shenzhen whwave SD3078 device

        s35390a                 Select the ABLIC S35390A device

        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"

        addr                    Sets the address for the RTC. Note that the
                                device must be configured to use the specified
                                address.

        trickle-diode-disable   Do not use the internal trickle charger diode
                                (BQ32000 only)

        trickle-diode-type      Diode type for trickle charge - "standard" or
                                "schottky" (ABx80x and RV1805 only)

        trickle-resistor-ohms   Resistor value for trickle charge (DS1339,
                                ABx80x, BQ32000, RV1805, RV3028, RV3032)

        trickle-voltage-mv      Charge pump voltage for trickle charge (RV3032)

        wakeup-source           Specify that the RTC can be used as a wakeup
                                source

        backup-switchover-mode  Backup power supply switch mode. Must be 0 for
                                "Switchover disabled", 1 for "Direct Switching"
                                (if Vdd < VBackup), 2 for "Standby
                                Mode" (if Vdd < Vbackup,
                                does not draw current) or 3 for
                                "Level Switching" (if Vdd < Vbackup
                                and Vdd < Vddsw and Vbackup > Vddsw)
                                (RV3028, RV3032)


Name:   i2c-rtc-gpio
Info:   Adds support for a number of I2C Real Time Clock devices
        using the software i2c controller
Load:   dtoverlay=i2c-rtc-gpio,<param>=<val>
Params: abx80x                  Select one of the ABx80x family:
                                  AB0801, AB0803, AB0804, AB0805,
                                  AB1801, AB1803, AB1804, AB1805

        bq32000                 Select the TI BQ32000 device

        ds1307                  Select the DS1307 device

        ds1339                  Select the DS1339 device

        ds1340                  Select the DS1340 device

        ds3231                  Select the DS3231 device

        m41t62                  Select the M41T62 device

        mcp7940x                Select the MCP7940x device

        mcp7941x                Select the MCP7941x device

        pcf2127                 Select the PCF2127 device

        pcf2129                 Select the PCF2129 device

        pcf2131                 Select the PCF2131 device

        pcf85063                Select the PCF85063 device

        pcf85063a               Select the PCF85063A device

        pcf8523                 Select the PCF8523 device

        pcf85363                Select the PCF85363 device

        pcf8563                 Select the PCF8563 device

        rv1805                  Select the Micro Crystal RV1805 device

        rv3028                  Select the Micro Crystal RV3028 device

        rv3032                  Select the Micro Crystal RV3032 device

        rv8803                  Select the Micro Crystal RV8803 device

        sd3078                  Select the ZXW Shenzhen whwave SD3078 device

        s35390a                 Select the ABLIC S35390A device

        addr                    Sets the address for the RTC. Note that the
                                device must be configured to use the specified
                                address.

        trickle-diode-disable   Do not use the internal trickle charger diode
                                (BQ32000 only)

        trickle-diode-type      Diode type for trickle charge - "standard" or
                                "schottky" (ABx80x and RV1805 only)

        trickle-resistor-ohms   Resistor value for trickle charge (DS1339,
                                ABx80x, BQ32000, RV1805, RV3028, RV3032)

        trickle-voltage-mv      Charge pump voltage for trickle charge (RV3032)

        wakeup-source           Specify that the RTC can be used as a wakeup
                                source

        backup-switchover-mode  Backup power supply switch mode. Must be 0 for
                                "Switchover disabled", 1 for "Direct Switching"
                                (if Vdd < VBackup), 2 for "Standby
                                Mode" (if Vdd < Vbackup,
                                does not draw current) or 3 for
                                "Level Switching" (if Vdd < Vbackup
                                and Vdd < Vddsw and Vbackup > Vddsw)
                                (RV3028, RV3032)

        i2c_gpio_sda            GPIO used for I2C data (default "23")

        i2c_gpio_scl            GPIO used for I2C clock (default "24")

        i2c_gpio_delay_us       Clock delay in microseconds
                                (default "2" = ~100kHz)


Name:   i2c-sensor
Info:   Adds support for a number of I2C barometric pressure, temperature,
        light level and chemical sensors on i2c_arm
Load:   dtoverlay=i2c-sensor,<param>=<val>
Params: addr                    Set the address for the ADT7410, AS73211,
                                AS7331, BH1750, BME280, BME680, BMP280, BMP380,
                                CCS811, DS1621, HDC100X, HDC3020, JC42, LM75,
                                MCP980x, MPU6050, MPU9250, MS5637, MS5803,
                                MS5805, MS5837, MS8607, SHT3x or TMP102

        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"

        adt7410                 Select the Analog Devices ADT7410 and ADT7420
                                temperature sensors
                                Valid address 0x48-0x4b, default 0x48

        aht10                   Select the Aosong AHT10 temperature and humidity
                                sensor

        as73211                 Select the AMS AS73211 XYZ true color sensor
                                Valid addresses 0x74-0x77, default 0x74

        as7331                  Select the AMS AS7331 spectral UV sensor
                                Valid addresses 0x74-0x77, default 0x74

        bh1750                  Select the Rohm BH1750 ambient light sensor
                                Valid addresses 0x23 or 0x5c, default 0x23

        bme280                  Select the Bosch Sensortronic BME280
                                Valid addresses 0x76-0x77, default 0x76

        bme680                  Select the Bosch Sensortronic BME680
                                Valid addresses 0x76-0x77, default 0x76

        bmp085                  Select the Bosch Sensortronic BMP085

        bmp180                  Select the Bosch Sensortronic BMP180

        bmp280                  Select the Bosch Sensortronic BMP280
                                Valid addresses 0x76-0x77, default 0x76

        bmp380                  Select the Bosch Sensortronic BMP380
                                Valid addresses 0x76-0x77, default 0x76

        bno055                  Select the Bosch Sensortronic BNO055 IMU
                                Valid address 0x28-0x29, default 0x29

        ccs811                  Select the AMS CCS811 digital gas sensor
                                Valid addresses 0x5a-0x5b, default 0x5b

        ds1621                  Select the Dallas Semiconductors DS1621 temp
                                sensor. Valid addresses 0x48-0x4f, default 0x48

        gain                    Gain used for measuring shunt resistor current.
                                Valid values 1 or 4, default 1. (ina238 only,
                                disabled by default)

        hdc100x                 Select the Texas Instruments HDC100x temp sensor
                                Valid addresses 0x40-0x43, default 0x40

        hdc3020                 Select the Texas Instruments HDC3020 temp sensor
                                Valid addresses 0x44-0x47, default 0x44

        hts221                  Select the HTS221 temperature and humidity
                                sensor

        htu21                   Select the HTU21 temperature and humidity sensor

        ina238                  Select the TI INA238 power monitor. Valid
                                addresses 0x40-0x4F, default 0x40.
                                Uses parameters shunt-resistor and
                                ti,shunt-gain for configuration

        int_pin                 Set the GPIO to use for interrupts (as73211,
                                as7331, hdc3020, hts221, max30102, mpu6050 and
                                mpu9250 only)

        jc42                    Select any of the many JEDEC JC42.4-compliant
                                temperature sensors, including:
                                  ADT7408, AT30TS00, CAT34TS02, CAT6095,
                                  MAX6604, MCP9804, MCP9805, MCP9808,
                                  MCP98242, MCP98243, MCP98244, MCP9843,
                                  SE97, SE98, STTS424(E), STTS2002, STTS3000,
                                  TSE2002, TSE2004, TS3000, and TS3001.
                                The default address is 0x18.

        lm75                    Select the Maxim LM75 temperature sensor
                                Valid addresses 0x48-0x4f, default 0x4f

        lm75addr                Deprecated - use addr parameter instead

        max17040                Select the Maxim Integrated MAX17040 battery
                                monitor

        max30102                Select the Maxim Integrated MAX30102 heart-rate
                                and blood-oxygen sensor

        mcp980x                 Select the Maxim MCP980x range of temperature
                                sensors (i.e. MCP9800, MCP9801, MCP9802 and
                                MCP9803). N.B. For MCP9804, MCP9805 and MCP9808,
                                use the "jc42" option.
                                Valid addresses are 0x18-0x1f (default 0x18)

        mpu6050                 Select the InvenSense MPU6050 IMU. Valid
                                valid addresses are 0x68 and 0x69 (default 0x68)

        mpu9250                 Select the InvenSense MPU9250 IMU. Valid
                                valid addresses are 0x68 and 0x69 (default 0x68)

        ms5637                  Select the Measurement Specialities MS5637
                                pressure and temperature sensor.

        ms5803                  Select the Measurement Specialities MS5803
                                pressure and temperature sensor.

        ms5805                  Select the Measurement Specialities MS5805
                                pressure and temperature sensor.

        ms5837                  Select the Measurement Specialities MS5837
                                pressure and temperature sensor.

        ms8607                  Select the Measurement Specialities MS8607
                                pressure and temperature sensor.

        no_timeout              Disable the SMBUS timeout. N.B. Only supported
                                by some jc42 devices - using with an
                                incompatible device can stop it from being
                                activated.

        reset_pin               GPIO to be used to reset the device (bno055
                                only, disabled by default)

        shunt_resistor          Value of shunt resistor used for current
                                measurement in uOhms.  (ina238 only, disabled
                                by default)

        sht3x                   Select the Sensirion SHT3x temperature and
                                humidity sensors. Valid addresses 0x44-0x45,
                                default 0x44

        sht4x                   Select the Sensirion SHT4x temperature and
                                humidity sensors. Valid addresses 0x44-0x45,
                                default 0x44

        shtc3                   Select the Sensirion SHTC3 temperature and
                                humidity sensors.

        si7020                  Select the Silicon Labs Si7013/20/21 humidity/
                                temperature sensor

        sps30                   Select the Sensirion SPS30 particulate matter
                                sensor. Fixed address 0x69.

        sgp30                   Select the Sensirion SGP30 VOC sensor.
                                Fixed address 0x58.

        tmp102                  Select the Texas Instruments TMP102 temp sensor
                                Valid addresses 0x48-0x4b, default 0x48

        tsl4531                 Select the AMS TSL4531 digital ambient light
                                sensor

        veml6070                Select the Vishay VEML6070 ultraviolet light
                                sensor

        veml6075                Select the Vishay VEML6075 UVA and UVB light
                                sensor


Name:   i2c0
Info:   Change i2c0 pin usage. Not all pin combinations are usable on all
        platforms - platforms other then Compute Modules can only use this
        to disable transaction combining.
        Do NOT use in conjunction with dtparam=i2c_vc=on. From the 5.4 kernel
        onwards the base DT includes the use of i2c_mux_pinctrl to expose two
        muxings of BSC0 - GPIOs 0&1, and whichever combination is used for the
        camera and display connectors. This overlay disables that mux and
        configures /dev/i2c0 to point at whichever set of pins is requested.
        dtparam=i2c_vc=on will try and enable the mux, so combining the two
        will cause conflicts.
Load:   dtoverlay=i2c0,<param>=<val>
Params: pins_0_1                Use pins 0 and 1 (default)
        pins_28_29              Use pins 28 and 29
        pins_44_45              Use pins 44 and 45
        pins_46_47              Use pins 46 and 47
        combine                 Allow transactions to be combined (default
                                "yes")


Name:   i2c0-bcm2708
Info:   Deprecated, legacy version of i2c0.
Load:   <Deprecated>


Name:   i2c0-pi5
Info:   Enable i2c0 (Pi 5 only)
Load:   dtoverlay=i2c0-pi5,<param>=<val>
Params: pins_0_1                Use GPIOs 0 and 1 (default)
        pins_8_9                Use GPIOs 8 and 9
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c1
Info:   Change i2c1 pin usage. Not all pin combinations are usable on all
        platforms - platforms other then Compute Modules can only use this
        to disable transaction combining.
Load:   dtoverlay=i2c1,<param>=<val>
Params: pins_2_3                Use pins 2 and 3 (default)
        pins_44_45              Use pins 44 and 45
        combine                 Allow transactions to be combined (default
                                "yes")


Name:   i2c1-bcm2708
Info:   Deprecated, legacy version of i2c1.
Load:   <Deprecated>


Name:   i2c1-pi5
Info:   Enable i2c1 (Pi 5 only)
Load:   dtoverlay=i2c1-pi5,<param>=<val>
Params: pins_2_3                Use GPIOs 2 and 3 (default)
        pins_10_11              Use GPIOs 10 and 11
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c2-pi5
Info:   Enable i2c2 (Pi 5 only)
Load:   dtoverlay=i2c2-pi5,<param>=<val>
Params: pins_4_5                Use GPIOs 4 and 5 (default)
        pins_12_13              Use GPIOs 12 and 13
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c3
Info:   Enable the i2c3 bus. BCM2711 only.
Load:   dtoverlay=i2c3,<param>
Params: pins_2_3                Use GPIOs 2 and 3
        pins_4_5                Use GPIOs 4 and 5 (default)
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c3-pi5
Info:   Enable i2c3 (Pi 5 only)
Load:   dtoverlay=i2c3-pi5,<param>=<val>
Params: pins_6_7                Use GPIOs 6 and 7 (default)
        pins_14_15              Use GPIOs 14 and 15
        pins_22_23              Use GPIOs 22 and 23
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c4
Info:   Enable the i2c4 bus. BCM2711 only.
Load:   dtoverlay=i2c4,<param>
Params: pins_6_7                Use GPIOs 6 and 7
        pins_8_9                Use GPIOs 8 and 9 (default)
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c5
Info:   Enable the i2c5 bus. BCM2711 only.
Load:   dtoverlay=i2c5,<param>
Params: pins_10_11              Use GPIOs 10 and 11
        pins_12_13              Use GPIOs 12 and 13 (default)
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2c6
Info:   Enable the i2c6 bus. BCM2711 only.
Load:   dtoverlay=i2c6,<param>
Params: pins_0_1                Use GPIOs 0 and 1
        pins_22_23              Use GPIOs 22 and 23 (default)
        baudrate                Set the baudrate for the interface (default
                                "100000")


Name:   i2s-dac
Info:   Configures any passive I2S DAC soundcard.
Load:   dtoverlay=i2s-dac
Params: <None>


Name:   i2s-gpio28-31
Info:   move I2S function block to GPIO 28 to 31
Load:   dtoverlay=i2s-gpio28-31
Params: <None>


Name:   i2s-master-dac
Info:   Configures a generic I2S DAC soundcard that acts as a clock master.
Load:   dtoverlay=i2s-master-dac
Params: <None>


Name:   ilitek251x
Info:   Enables I2C connected Ilitek 251x multiple touch controller using
        GPIO 4 (pin 7 on GPIO header) for interrupt.
Load:   dtoverlay=ilitek251x,<param>=<val>
Params: interrupt               GPIO used for interrupt (default 4)
        sizex                   Touchscreen size x, horizontal resolution of
                                touchscreen (in pixels)
        sizey                   Touchscreen size y, vertical resolution of
                                touchscreen (in pixels)
        i2c-path                Override I2C path to allow for i2c-gpio buses


Name:   imx219
Info:   Sony IMX219 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx219,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Configure a VCM focus drive on the sensor.
        4lane                   Enable 4 CSI2 lanes. This requires a Compute
                                Module (1, 3, 4, or 5) or Pi 5.


Name:   imx258
Info:   Sony IMX258 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx258,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Configure a VCM focus drive on the sensor.
        4lane                   Enable 4 CSI2 lanes. This requires a Compute
                                Module (1, 3, or 4).


Name:   imx290
Info:   Sony IMX290 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx290,<param>
Params: 4lane                   Enable 4 CSI2 lanes. This requires a Compute
                                Module (1, 3, or 4).
        clock-frequency         Sets the clock frequency to match that used on
                                the board.
                                Modules from Vision Components use 37.125MHz
                                (the default), whilst those from Innomaker use
                                74.25MHz.
        mono                    Denote that the module is a mono sensor.
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   imx296
Info:   Sony IMX296 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx296,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        clock-frequency         Sets the clock frequency to match that used on
                                the board, which should be one of 54000000
                                (the default), 37125000 or 74250000.
        always-on               Leave the regulator powered up, to stop the
                                camera clamping I/Os such as XTRIG to 0V.


Name:   imx327
Info:   Sony IMX327 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx327,<param>
Params: 4lane                   Enable 4 CSI2 lanes. This requires a Compute
                                Module (1, 3, or 4).
        clock-frequency         Sets the clock frequency to match that used on
                                the board.
                                Modules from Vision Components use 37.125MHz
                                (the default), whilst those from Innomaker use
                                74.25MHz.
        mono                    Denote that the module is a mono sensor.
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   imx378
Info:   Sony IMX378 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx378,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        always-on               Leave the regulator powered up, to stop the
                                camera clamping I/Os such as XVS to 0V.
        sync-source             Configure as vsync source
        sync-sink               Configure as vsync sink
        link-frequency          Allowable link frequency values to use in Hz:
                                450000000 (default), 453000000, 456000000.


Name:   imx415
Info:   Sony IMX415 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants. By default this uses 4 CSI2 data lanes, so requires a
        Compute Module or Pi5.
Load:   dtoverlay=imx415,<param>
Params: addr                    Set I2C address of sensor. Valid values are
                                0x10, 0x1a, 0x36 and 0x37. Default is 0x37.
        4lane                   Enable 4 CSI2 data lanes.
        clock-frequency         Sets the clock frequency to match that used on
                                the board.
                                Valid values are 24, 27, 37.125, 72, or
                                74.25MHz.
                                The default is 24MHz.
                                Note that the link frequencies permitted vary
                                based on the oscillator used.
        link-frequency          Confgures the link frequency to be used. Note
                                that the permitted values vary based on
                                clock-frequency and number of lanes.
                                The default is 360MHz for 720Mbit/s.
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Enable ad5398 VCM associated with the sensor.


Name:   imx462
Info:   Sony IMX462 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx462,<param>
Params: 4lane                   Enable 4 CSI2 lanes. This requires a Compute
                                Module (1, 3, or 4).
        clock-frequency         Sets the clock frequency to match that used on
                                the board.
                                Modules from Vision Components use 37.125MHz
                                (the default), whilst those from Innomaker use
                                74.25MHz.
        mono                    Denote that the module is a mono sensor.
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   imx477
Info:   Sony IMX477 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx477,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        always-on               Leave the regulator powered up, to stop the
                                camera clamping I/Os such as XVS to 0V.
        sync-source             Configure as vsync source
        sync-sink               Configure as vsync sink
        link-frequency          Allowable link frequency values to use in Hz:
                                450000000 (default), 453000000, 456000000.


Name:   imx500
Info:   Sony IMX500 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx500,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        bypass-cache            Do save blocks of data to flash when using
                                rp2040-gpio-bridge for SPI transfers.


Name:   imx500-pi5
Info:   See imx500 (this is the Pi 5 version)


Name:   imx519
Info:   Sony IMX519 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx519,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Select lens driver state. Default is enabled,
                                but vcm=off will disable.


Name:   imx708
Info:   Sony IMX708 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=imx708,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 180)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        vcm                     Select lens driver state. Default is enabled,
                                but vcm=off will disable.
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        link-frequency          Allowable link frequency values to use in Hz:
                                450000000 (default), 447000000, 453000000.


Name:   interludeaudio-analog
Info:   Configures Interlude Audio Analog Hat audio card
Load:   dtoverlay=interludeaudio-analog,<param>=<val>
Params: gpiopin                 GPIO pin for codec reset


Name:   interludeaudio-digital
Info:   Configures Interlude Audio Digital Hat audio card
Load:   dtoverlay=interludeaudio-digital
Params: <None>


Name:   iqaudio-codec
Info:   Configures the IQaudio Codec audio card
Load:   dtoverlay=iqaudio-codec
Params: <None>


Name:   iqaudio-dac
Info:   Configures the IQaudio DAC audio card
Load:   dtoverlay=iqaudio-dac,<param>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=iqaudio-dac,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   iqaudio-dacplus
Info:   Configures the IQaudio DAC+ audio card
Load:   dtoverlay=iqaudio-dacplus,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=iqaudio-dacplus,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)
        auto_mute_amp           If specified, unmute/mute the IQaudIO amp when
                                starting/stopping audio playback.
        unmute_amp              If specified, unmute the IQaudIO amp once when
                                the DAC driver module loads.


Name:   iqaudio-digi-wm8804-audio
Info:   Configures the IQAudIO Digi WM8804 audio card
Load:   dtoverlay=iqaudio-digi-wm8804-audio,<param>=<val>
Params: card_name               Override the default, "IQAudIODigi", card name.
        dai_name                Override the default, "IQAudIO Digi", dai name.
        dai_stream_name         Override the default, "IQAudIO Digi HiFi",
                                dai stream name.


Name:   iqs550
Info:   Enables I2C connected Azoteq IQS550 trackpad/touchscreen controller
        using GPIO 4 (pin 7 on GPIO header) for interrupt.
Load:   dtoverlay=iqs550,<param>=<val>
Params: interrupt               GPIO used for interrupt (default 4)
        reset                   GPIO used for reset (optional)
        sizex                   Touchscreen size x (default 800)
        sizey                   Touchscreen size y (default 480)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis


Name:   irs1125
Info:   Infineon irs1125 TOF camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=irs1125,<param>=<val>
Params: media-controller        Configure use of Media Controller API for
                                configuring the sensor (default off)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   jedec-spi-nor
Info:   Adds support for JEDEC-compliant SPI NOR flash devices.  (Note: The
        "jedec,spi-nor" kernel driver was formerly known as "m25p80".)
Load:   dtoverlay=jedec-spi-nor,<param>=<val>
Params: spi<n>-<m>              Enable flash device on SPI<n>, CS#<m>
        fastr                   Add fast read capability to the flash device
        speed                   Maximum SPI frequency (Hz)
        flash-spi<n>-<m>        Same as spi<n>-<m> (deprecated)
        flash-fastr-spi<n>-<m>  Same as spi<n>->m>,fastr (deprecated)


Name:   justboom-both
Info:   Simultaneous usage of an justboom-dac and justboom-digi based
        card
Load:   dtoverlay=justboom-both,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=justboom-dac,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   justboom-dac
Info:   Configures the JustBoom DAC HAT, Amp HAT, DAC Zero and Amp Zero audio
        cards
Load:   dtoverlay=justboom-dac,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                Digital volume control. Enable with
                                "dtoverlay=justboom-dac,24db_digital_gain"
                                (The default behaviour is that the Digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24dB_digital_gain parameter, the Digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the Digital volume control is set to a value
                                that does not result in clipping/distortion!)


Name:   justboom-digi
Info:   Configures the JustBoom Digi HAT and Digi Zero audio cards
Load:   dtoverlay=justboom-digi
Params: <None>


Name:   lirc-rpi
Info:   This overlay has been deprecated and removed - see gpio-ir
Load:   <Deprecated>


Name:   ltc294x
Info:   Adds support for the ltc294x family of battery gauges
Load:   dtoverlay=ltc294x,<param>=<val>
Params: ltc2941                 Select the ltc2941 device

        ltc2942                 Select the ltc2942 device

        ltc2943                 Select the ltc2943 device

        ltc2944                 Select the ltc2944 device

        resistor-sense          The sense resistor value in milli-ohms.
                                Can be a 32-bit negative value when the battery
                                has been connected to the wrong end of the
                                resistor.

        prescaler-exponent      Range and accuracy of the gauge. The value is
                                programmed into the chip only if it differs
                                from the current setting.
                                For LTC2941 only:
                                - Default value is 128
                                - the exponent is in the range 0-7 (default 7)
                                See the datasheet for more information.


Name:   max98357a
Info:   Configures the Maxim MAX98357A I2S DAC
Load:   dtoverlay=max98357a,<param>=<val>
Params: no-sdmode               Driver does not manage the state of the DAC's
                                SD_MODE pin (i.e. chip is always on).
        sdmode-pin              integer, GPIO pin connected to the SD_MODE input
                                of the DAC (default GPIO4 if parameter omitted).


Name:   maxtherm
Info:   Configure a MAX6675, MAX31855 or MAX31856 thermocouple as an IIO device.

        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
        The overlay expects to disable the relevant spidev node, so also using
        e.g. cs0_spidev=off is unnecessary.

        Example:
        MAX31855 on /dev/spidev0.0
            dtoverlay=maxtherm,spi0-0,max31855
        MAX31856 using a type J thermocouple on /dev/spidev2.1
            dtoverlay=spi2-2cs
            dtoverlay=maxtherm,spi2-1,max31856,type_j

Load:   dtoverlay=maxtherm,<param>=<val>
Params: spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)
        max6675                 Enable support for the MAX6675 (default)
        max31855                Enable support for the MAX31855
        max31855e               Enable support for the MAX31855E
        max31855j               Enable support for the MAX31855J
        max31855k               Enable support for the MAX31855K
        max31855n               Enable support for the MAX31855N
        max31855r               Enable support for the MAX31855R
        max31855s               Enable support for the MAX31855S
        max31855t               Enable support for the MAX31855T
        max31856                Enable support for the MAX31856 (with type K)
        type_b                  Select a type B sensor for max31856
        type_e                  Select a type E sensor for max31856
        type_j                  Select a type J sensor for max31856
        type_k                  Select a type K sensor for max31856
        type_n                  Select a type N sensor for max31856
        type_r                  Select a type R sensor for max31856
        type_s                  Select a type S sensor for max31856
        type_t                  Select a type T sensor for max31856


Name:   mbed-dac
Info:   Configures the mbed AudioCODEC (TLV320AIC23B)
Load:   dtoverlay=mbed-dac
Params: <None>


Name:   mcp23017
Info:   Configures the MCP23017 I2C GPIO expander
Load:   dtoverlay=mcp23017,<param>=<val>
Params: gpiopin                 Gpio pin connected to the INTA output of the
                                MCP23017 (default: 4)

        addr                    I2C address of the MCP23017 (default: 0x20)

        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"

        mcp23008                Configure an MCP23008 instead.
        noints                  Disable the interrupt GPIO line.


Name:   mcp23s17
Info:   Configures the MCP23S08/17 SPI GPIO expanders.
        If devices are present on SPI1 or SPI2, those interfaces must be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
        If interrupts are enabled for a device on a given CS# on a SPI bus, that
        device must be the only one present on that SPI bus/CS#.
Load:   dtoverlay=mcp23s17,<param>=<val>
Params: s08-spi<n>-<m>-present  4-bit integer, bitmap indicating MCP23S08
                                devices present on SPI<n>, CS#<m>

        s17-spi<n>-<m>-present  8-bit integer, bitmap indicating MCP23S17
                                devices present on SPI<n>, CS#<m>

        s08-spi<n>-<m>-int-gpio integer, enables interrupts on a single
                                MCP23S08 device on SPI<n>, CS#<m>, specifies
                                the GPIO pin to which INT output of MCP23S08
                                is connected.

        s17-spi<n>-<m>-int-gpio integer, enables mirrored interrupts on a
                                single MCP23S17 device on SPI<n>, CS#<m>,
                                specifies the GPIO pin to which either INTA
                                or INTB output of MCP23S17 is connected.


Name:   mcp2515
Info:   Configures the MCP2515 CAN controller on spi0/1/2
        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
Load:   dtoverlay=mcp2515,<param>=<val>
Params: spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)

        oscillator              Clock frequency for the CAN controller (Hz)

        speed                   Maximum SPI frequence (Hz)

        interrupt               GPIO for interrupt signal


Name:   mcp2515-can0
Info:   Configures the MCP2515 CAN controller on spi0.0
Load:   dtoverlay=mcp2515-can0,<param>=<val>
Params: oscillator              Clock frequency for the CAN controller (Hz)

        spimaxfrequency         Maximum SPI frequence (Hz)

        interrupt               GPIO for interrupt signal


Name:   mcp2515-can1
Info:   Configures the MCP2515 CAN controller on spi0.1
Load:   dtoverlay=mcp2515-can1,<param>=<val>
Params: oscillator              Clock frequency for the CAN controller (Hz)

        spimaxfrequency         Maximum SPI frequence (Hz)

        interrupt               GPIO for interrupt signal


Name:   mcp251xfd
Info:   Configures the MCP251XFD CAN controller family
        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
Load:   dtoverlay=mcp251xfd,<param>=<val>
Params: spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)

        oscillator              Clock frequency for the CAN controller (Hz)

        speed                   Maximum SPI frequence (Hz)

        interrupt               GPIO for interrupt signal

        rx_interrupt            GPIO for RX interrupt signal (nINT1) (optional)

        xceiver_enable          GPIO for CAN transceiver enable (optional)

        xceiver_active_high     specifiy if CAN transceiver enable pin is
                                active high (optional, default: active low)


Name:   mcp3008
Info:   Configures MCP3008 A/D converters
        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
Load:   dtoverlay=mcp3008,<param>[=<val>]
Params: spi<n>-<m>-present      boolean, configure device at spi<n>, cs<m>
        spi<n>-<m>-speed        integer, set the spi bus speed for this device


Name:   mcp3202
Info:   Configures MCP3202 A/D converters
        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.
Load:   dtoverlay=mcp3202,<param>[=<val>]
Params: spi<n>-<m>-present      boolean, configure device at spi<n>, cs<m>
        spi<n>-<m>-speed        integer, set the spi bus speed for this device


Name:   mcp342x
Info:   Overlay for activation of Microchip MCP3421-3428 ADCs over I2C
Load:   dtoverlay=mcp342x,<param>=<val>
Params: addr                    I2C bus address of device, for devices with
                                addresses that are configurable, e.g. by
                                hardware links (default=0x68)
        mcp3421                 The device is an MCP3421
        mcp3422                 The device is an MCP3422
        mcp3423                 The device is an MCP3423
        mcp3424                 The device is an MCP3424
        mcp3425                 The device is an MCP3425
        mcp3426                 The device is an MCP3426
        mcp3427                 The device is an MCP3427
        mcp3428                 The device is an MCP3428


Name:   media-center
Info:   Media Center HAT - 2.83" Touch Display + extras by Pi Supply
Load:   dtoverlay=media-center,<param>=<val>
Params: speed                   Display SPI bus speed
        rotate                  Display rotation {0,90,180,270}
        fps                     Delay between frame updates
        xohms                   Touchpanel sensitivity (X-plate resistance)
        swapxy                  Swap x and y axis
        backlight               Change backlight GPIO pin {e.g. 12, 18}
        debug                   "on" = enable additional debug messages
                                (default "off")


Name:   merus-amp
Info:   Configures the merus-amp audio card
Load:   dtoverlay=merus-amp
Params: <None>


Name:   midi-uart0
Info:   Configures UART0 (ttyAMA0) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart0
Params: <None>


Name:   midi-uart0-pi5
Info:   See midi-uart0 (this is the Pi 5 version)


Name:   midi-uart1
Info:   Configures UART1 (ttyS0) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart1
Params: <None>


Name:   midi-uart1-pi5
Info:   See midi-uart1 (this is the Pi 5 version)


Name:   midi-uart2
Info:   Configures UART2 (ttyAMA2) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart2
Params: <None>


Name:   midi-uart2-pi5
Info:   See midi-uart2 (this is the Pi 5 version)


Name:   midi-uart3
Info:   Configures UART3 (ttyAMA3) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart3
Params: <None>


Name:   midi-uart3-pi5
Info:   See midi-uart3 (this is the Pi 5 version)


Name:   midi-uart4
Info:   Configures UART4 (ttyAMA4) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart4
Params: <None>


Name:   midi-uart4-pi5
Info:   See midi-uart4 (this is the Pi 5 version)


Name:   midi-uart5
Info:   Configures UART5 (ttyAMA5) so that a requested 38.4kbaud actually gets
        31.25kbaud, the frequency required for MIDI
Load:   dtoverlay=midi-uart5
Params: <None>


Name:   minipitft13
Info:   Overlay for AdaFruit Mini Pi 1.3" TFT via SPI using fbtft driver.
Load:   dtoverlay=minipitft13,<param>=<val>
Params: speed                   SPI bus speed (default 32000000)
        rotate                  Display rotation (0, 90, 180 or 270; default 0)
        width                   Display width (default 240)
        height                  Display height (default 240)
        fps                     Delay between frame updates (default 25)
        debug                   Debug output level (0-7; default 0)


Name:   miniuart-bt
Info:   Switch the onboard Bluetooth function of a BT-equipped Raspberry Pi
        to use the mini-UART (ttyS0) and restore UART0/ttyAMA0 over GPIOs 14 &
        15. Note that this option uses a lower baudrate, and should only be used
        with low-bandwidth peripherals.
Load:   dtoverlay=miniuart-bt,<param>=<val>
Params: krnbt                   Set to "off" to disable autoprobing of Bluetooth
                                driver without need of hciattach/btattach


Name:   mipi-dbi-spi
Info:   Overlay for SPI-connected MIPI DBI displays using the panel-mipi-dbi
        driver. The driver will load a file /lib/firmware/panel.bin containing
        the initialisation commands.

        Example:
          dtoverlay=mipi-dbi-spi,spi0-0,speed=70000000
          dtparam=width=320,height=240
          dtparam=reset-gpio=23,dc-gpio=24
          dtparam=backlight-gpio=18

        Compared to fbtft panel-mipi-dbi runs pixel data at spi-max-frequency
        and init commands at 10MHz. This makes it possible to push the envelope
        without messing up the controller configuration due to command
        transmission errors.

        For devices on spi1 or spi2, the interfaces should be enabled
        with one of the spi1-1/2/3cs and/or spi2-1/2/3cs overlays.

        See https://github.com/notro/panel-mipi-dbi/wiki for more info.

Load:   dtoverlay=mipi-dbi-spi,<param>=<val>
Params:
        compatible              Set the compatible string to load a different
                                firmware file. Both the panel compatible value
                                used to load the firmware file and the value
                                used to load the driver has to be set having a
                                NUL (\0) separator between them.
                                Example:
                                dtparam=compatible=mypanel\0panel-mipi-dbi-spi
        spi<n>-<m>              Configure device at spi<n>, cs<m>
                                (boolean, required)
        speed                   SPI bus speed in Hz (default 32000000)
        cpha                    Shifted SPI clock phase (CPHA) mode
        cpol                    Inverse SPI clock polarity (CPOL) mode
        write-only              Controller is not readable
                                (ie. MISO is not wired up).

        width                   Panel width in pixels (required)
        height                  Panel height in pixels (required)
        width-mm                Panel width in mm
        height-mm               Panel height in mm
        x-offset                Panel x-offset in controller RAM
        y-offset                Panel y-offset in controller RAM

        clock-frequency         Panel clock frequency in Hz
                                (optional, just informational).

        reset-gpio              GPIO pin to be used for RESET
        dc-gpio                 GPIO pin to be used for D/C

        backlight-gpio          GPIO pin to be used for backlight control
                                (default of none).
        backlight-pwm           PWM channel to be used for backlight control
                                (default of none). NB Disables audio headphone
                                output as that also uses PWM.
        backlight-pwm-chan      Choose channel on &pwm node for backlight
                                control (default 0).
        backlight-pwm-gpio      GPIO pin to be used for the PWM backlight. See
                                pwm-2chan for valid options (default 18).
        backlight-pwm-func      Pin function of GPIO used for the PWM backlight.
                                See pwm-2chan for valid options (default 2).
        backlight-def-brightness
                                Set the default brightness. Normal range 1-16.
                                (default 16).


Name:   mlx90640
Info:   Overlay for i2c connected mlx90640 thermal camera
Load:   dtoverlay=mlx90640
Params: <None>


Name:   mmc
Info:   Selects the bcm2835-mmc SD/MMC driver, optionally with overclock
Load:   dtoverlay=mmc,<param>=<val>
Params: overclock_50            Clock (in MHz) to use when the MMC framework
                                requests 50MHz


Name:   mpu6050
Info:   This overlay has been deprecated - use "dtoverlay=i2c-sensor,mpu6050"
        instead. Note that "int_pin" is the new name for the "interrupt"
        parameter.
Load:   <Deprecated>


Name:   mz61581
Info:   MZ61581 display by Tontec
Load:   dtoverlay=mz61581,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        txbuflen                Transmit buffer length (default 32768)

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)


Name:   ov2311
Info:   Omnivision OV2311 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=ov2311,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   ov5647
Info:   Omnivision OV5647 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=ov5647,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Configure a VCM focus drive on the sensor.


Name:   ov64a40
Info:   Arducam OV64A40 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=ov64a40,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        vcm                     Select lens driver state. Default is enabled,
                                but vcm=off will disable.
        link-frequency          Allowable link frequency values to use in Hz:
                                456000000 (default), 360000000


Name:   ov7251
Info:   Omnivision OV7251 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=ov7251,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default off)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   ov9281
Info:   Omnivision OV9281 camera module.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=ov9281,<param>=<val>
Params: rotation                Mounting rotation of the camera sensor (0 or
                                180, default 0)
        orientation             Sensor orientation (0 = front, 1 = rear,
                                2 = external, default external)
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default on)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).
        arducam                 Slow down the regulator for slow Arducam
                                modules.


Name:   papirus
Info:   PaPiRus ePaper Screen by Pi Supply (both HAT and pHAT)
Load:   dtoverlay=papirus,<param>=<val>
Params: panel                   Display panel (required):
                                1.44": e1144cs021
                                2.0":  e2200cs021
                                2.7":  e2271cs021

        speed                   Display SPI bus speed


Name:   pca953x
Info:   TI PCA953x family of I2C GPIO expanders. Default is for NXP PCA9534.
Load:   dtoverlay=pca953x,<param>=<val>
Params: addr                    I2C address of expander. Default 0x20.
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"
        pca6416                 Select the NXP PCA6416 (16 bit)
        pca9505                 Select the NXP PCA9505 (40 bit)
        pca9535                 Select the NXP PCA9535 (16 bit)
        pca9536                 Select the NXP PCA9536 or TI PCA9536 (4 bit)
        pca9537                 Select the NXP PCA9537 (4 bit)
        pca9538                 Select the NXP PCA9538 (8 bit)
        pca9539                 Select the NXP PCA9539 (16 bit)
        pca9554                 Select the NXP PCA9554 (8 bit)
        pca9555                 Select the NXP PCA9555 (16 bit)
        pca9556                 Select the NXP PCA9556 (8 bit)
        pca9557                 Select the NXP PCA9557 (8 bit)
        pca9574                 Select the NXP PCA9574 (8 bit)
        pca9575                 Select the NXP PCA9575 (16 bit)
        pca9698                 Select the NXP PCA9698 (40 bit)
        pcal6416                Select the NXP PCAL6416 (16 bit)
        pcal6524                Select the NXP PCAL6524 (24 bit)
        pcal9555a               Select the NXP PCAL9555A (16 bit)
        max7310                 Select the Maxim MAX7310 (8 bit)
        max7312                 Select the Maxim MAX7312 (16 bit)
        max7313                 Select the Maxim MAX7313 (16 bit)
        max7315                 Select the Maxim MAX7315 (8 bit)
        pca6107                 Select the TI PCA6107 (8 bit)
        tca6408                 Select the TI TCA6408 (8 bit)
        tca6416                 Select the TI TCA6416 (16 bit)
        tca6424                 Select the TI TCA6424 (24 bit)
        tca9539                 Select the TI TCA9539 (16 bit)
        tca9554                 Select the TI TCA9554 (8 bit)
        cat9554                 Select the Onnn CAT9554 (8 bit)
        pca9654                 Select the Onnn PCA9654 (8 bit)
        xra1202                 Select the Exar XRA1202 (8 bit)


Name:   pcf857x
Info:   NXP PCF857x family of I2C GPIO expanders.
Load:   dtoverlay=pcf857x,<param>=<val>
Params: addr                    I2C address of expander. Default
                                depends on model selected.
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"
        pcf8574                 Select the NXP PCF8574 (8 bit)
        pcf8574a                Select the NXP PCF8574A (8 bit)
        pcf8575                 Select the NXP PCF8575 (16 bit)
        pca8574                 Select the NXP PCA8574 (8 bit)


Name:   pcie-32bit-dma
Info:   Force PCIe config to support 32bit DMA addresses at the expense of
        having to bounce buffers.
Load:   dtoverlay=pcie-32bit-dma
Params: <None>


Name:   pcie-32bit-dma-pi5
Info:   Force PCIe config to support 32bit DMA addresses at the expense of
        having to bounce buffers (on the Pi 5).
Load:   dtoverlay=pcie-32bit-dma-pi5
Params: <None>


Name:   pciex1-compat-pi5
Info:   Compatibility features for pciex1 on Pi 5.
Load:   dtoverlay=pciex1-compat-pi5,<param>=<val>
Params: l1ss                    Enable ASPM L1 sub-state support
        no-l0s                  Disable ASPM L0s
        no-mip                  Revert to the MSI target in the RC, instead of
                                the MSI-MIP peripheral. Use if a) more than 8
                                interrupt vectors are required or b) the EP
                                requires DMA and MSI addresses to be 32bit.
        mmio-hi                 Move the start of outbound 32bit addresses to
                                2GB and expand 64bit outbound space to 14GB.


[ The pcf2127-rtc overlay has been deleted. See i2c-rtc. ]


[ The pcf8523-rtc overlay has been deleted. See i2c-rtc. ]


[ The pcf8563-rtc overlay has been deleted. See i2c-rtc. ]


Name:   pi3-act-led
Info:   This overlay has been renamed act-led, keeping pi3-act-led as an alias
        for backwards compatibility.
Load:   <Deprecated>


Name:   pi3-disable-bt
Info:   This overlay has been renamed disable-bt, keeping pi3-disable-bt as an
        alias for backwards compatibility.
Load:   <Deprecated>


Name:   pi3-disable-wifi
Info:   This overlay has been renamed disable-wifi, keeping pi3-disable-wifi as
        an alias for backwards compatibility.
Load:   <Deprecated>


Name:   pi3-miniuart-bt
Info:   This overlay has been renamed miniuart-bt, keeping pi3-miniuart-bt as
        an alias for backwards compatibility.
Load:   <Deprecated>


Name:   pibell
Info:   Configures the pibell audio card.
Load:   dtoverlay=pibell,<param>=<val>
Params: alsaname                Set the name as it appears in ALSA (default
                                "PiBell")


Name:   pifacedigital
Info:   Configures the PiFace Digital mcp23s17 GPIO port expander.
Load:   dtoverlay=pifacedigital,<param>=<val>
Params: spi-present-mask        8-bit integer, bitmap indicating MCP23S17 SPI0
                                CS0 address. PiFace Digital supports addresses
                                0-3, which can be configured with JP1 and JP2.


Name:   pifi-40
Info:   Configures the PiFi 40W stereo amplifier
Load:   dtoverlay=pifi-40
Params: <None>


Name:   pifi-dac-hd
Info:   Configures the PiFi DAC HD
Load:   dtoverlay=pifi-dac-hd
Params: <None>


Name:   pifi-dac-zero
Info:   Configures the PiFi DAC Zero
Load:   dtoverlay=pifi-dac-zero
Params: <None>


Name:   pifi-mini-210
Info:   Configures the PiFi Mini stereo amplifier
Load:   dtoverlay=pifi-mini-210
Params: <None>


Name:   piglow
Info:   Configures the PiGlow by pimoroni.com
Load:   dtoverlay=piglow
Params: <None>


Name:   pimidi
Info:   Configures the Blokas Labs Pimidi card
Load:   dtoverlay=pimidi,<param>=<val>
Params: sel                     The position used for the sel rotary switch.
                                Each unit in the stack must be set on a unique
                                position. If param is omitted, sel=0 is assumed.


Name:   pineboards-hat-ai
Info:   Pineboards Hat Ai! overlay for the Google Coral Edge TPU
Load:   dtoverlay=pineboards-hat-ai
Params: <None>


Name:   pineboards-hatdrive-poe-plus
Info:   Configures the Pineboards HatDrive! PoE+
Load:   dtoverlay=pineboards-hatdrive-poe-plus
Params: <None>


Name:   piscreen
Info:   PiScreen display by OzzMaker.com
Load:   dtoverlay=piscreen,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)

        drm                     Select the DRM/KMS driver instead of the FBTFT
                                one

        invx                    Touchscreen inverted x axis

        invy                    Touchscreen inverted y axis

        swapxy                  Touchscreen swapped x y axis


Name:   piscreen2r
Info:   PiScreen 2 with resistive TP display by OzzMaker.com
Load:   dtoverlay=piscreen2r,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        xohms                   Touchpanel sensitivity (X-plate resistance)


Name:   pisound
Info:   Configures the Blokas Labs Pisound card
Load:   dtoverlay=pisound
Params: <None>


Name:   pisound-pi5
Info:   Pi 5 specific overlay override for Blokas Labs Pisound card, see pisound
Load:   dtoverlay=pisound-pi5
Params: <None>


Name:   pitft22
Info:   Adafruit PiTFT 2.2" screen
Load:   dtoverlay=pitft22,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        drm                     Force the use of the mi0283qt DRM driver (by
                                default the ili9340 framebuffer driver will
                                be used in preference if available)


Name:   pitft28-capacitive
Info:   Adafruit PiTFT 2.8" capacitive touch screen
Load:   dtoverlay=pitft28-capacitive,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        drm                     Force the use of the mi0283qt DRM driver (by
                                default the ili9340 framebuffer driver will
                                be used in preference if available)

        touch-sizex             Touchscreen size x (default 240)

        touch-sizey             Touchscreen size y (default 320)

        touch-invx              Touchscreen inverted x axis

        touch-invy              Touchscreen inverted y axis

        touch-swapxy            Touchscreen swapped x y axis


Name:   pitft28-resistive
Info:   Adafruit PiTFT 2.8" resistive touch screen
Load:   dtoverlay=pitft28-resistive,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        drm                     Force the use of the mi0283qt DRM driver (by
                                default the ili9340 framebuffer driver will
                                be used in preference if available)

        touch-invx              Touchscreen inverted x axis

        touch-invy              Touchscreen inverted y axis

        touch-swapxy            Touchscreen swapped x y axis


Name:   pitft35-resistive
Info:   Adafruit PiTFT 3.5" resistive touch screen
Load:   dtoverlay=pitft35-resistive,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        drm                     Force the use of the hx8357d DRM driver (by
                                default the fb_hx8357d framebuffer driver will
                                be used in preference if available)

        touch-invx              Touchscreen inverted x axis

        touch-invy              Touchscreen inverted y axis

        touch-swapxy            Touchscreen swapped x y axis


Name:   pps-gpio
Info:   Configures the pps-gpio (pulse-per-second time signal via GPIO).
Load:   dtoverlay=pps-gpio,<param>=<val>
Params: gpiopin                 Input GPIO (default "18")
        assert_falling_edge     When present, assert is indicated by a falling
                                edge, rather than by a rising edge (default
                                off)
        capture_clear           Generate clear events on the trailing edge
                                (default off)
        pull                    Desired pull-up/down state (off, down, up)
                                Default is "off".


Name:   proto-codec
Info:   Configures the PROTO Audio Codec card
Load:   dtoverlay=proto-codec
Params: <None>


Name:   pwm
Info:   Configures a single PWM channel
        Legal pin,function combinations for each channel:
          PWM0: 12,4(Alt0) 18,2(Alt5) 40,4(Alt0)            52,5(Alt1)
          PWM1: 13,4(Alt0) 19,2(Alt5) 41,4(Alt0) 45,4(Alt0) 53,5(Alt1)
        N.B.:
          1) Pin 18 is the only one available on all platforms, and
             it is the one used by the I2S audio interface.
             Pins 12 and 13 might be better choices on an A+, B+ or Pi2.
          2) The onboard analogue audio output uses both PWM channels.
          3) So be careful mixing audio and PWM.
          4) Currently the clock must have been enabled and configured
             by other means.
Load:   dtoverlay=pwm,<param>=<val>
Params: pin                     Output pin (default 18) - see table
        func                    Pin function (default 2 = Alt5) - see above
        clock                   PWM clock frequency (informational)


Name:   pwm-2chan
Info:   Configures both PWM channels
        Legal pin,function combinations for each channel:
          PWM0: 12,4(Alt0) 18,2(Alt5) 40,4(Alt0)            52,5(Alt1)
          PWM1: 13,4(Alt0) 19,2(Alt5) 41,4(Alt0) 45,4(Alt0) 53,5(Alt1)
        N.B.:
          1) Pin 18 is the only one available on all platforms, and
             it is the one used by the I2S audio interface.
             Pins 12 and 13 might be better choices on an A+, B+ or Pi2.
          2) The onboard analogue audio output uses both PWM channels.
          3) So be careful mixing audio and PWM.
          4) Currently the clock must have been enabled and configured
             by other means.
Load:   dtoverlay=pwm-2chan,<param>=<val>
Params: pin                     Output pin (default 18) - see table
        pin2                    Output pin for other channel (default 19)
        func                    Pin function (default 2 = Alt5) - see above
        func2                   Function for pin2 (default 2 = Alt5)
        clock                   PWM clock frequency (informational)


Name:   pwm-gpio
Info:   Configures the software PWM GPIO driver
Load:   dtoverlay=pwm-gpio,<param>=<val>
Params: gpio                    Output pin (default 4)


Name:   pwm-gpio-fan
Info:   Configure a GPIO connected PWM cooling fan controlled by the
        software-based GPIO PWM kernel module
Load:   dtoverlay=pwm-gpio-fan,<param>=<val>
Params: fan_gpio                BCM number of the pin driving the fan,
                                default 18 (GPIO 18)
        fan_temp0               CPU temperature at which fan is started with
                                low speed in millicelsius,
                                default 55000 (55 °C)
        fan_temp1               CPU temperature at which fan is switched
                                to medium speed in millicelsius,
                                default 60000 (60 °C)
        fan_temp2               CPU temperature at which fan is switched
                                to high speed in millicelsius,
                                default 67500 (67.5 °C)
        fan_temp3               CPU temperature at which fan is switched
                                to max speed in millicelsius,
                                default 75000 (75 °C)
        fan_temp0_hyst          Temperature hysteris at which fan is stopped
                                in millicelsius,default 5000 (resulting
                                in 50 °C)
        fan_temp1_hyst          Temperature hysteris at which fan is switched
                                back to low speed in millicelsius,
                                default 5000 (resulting in 55 °C)
        fan_temp2_hyst          Temperature hysteris at which fan is switched
                                back to medium speed in millicelsius,
                                default 5000 (resulting in 62.5 °C)
        fan_temp3_hyst          Temperature hysteris at which fan is switched
                                back to high speed in millicelsius,
                                default 5000 (resulting in 70 °C)
        fan_temp0_speed         Fan speed for low cooling state in range
                                0 to 255, default 114 (45% PWM duty cycle)
        fan_temp1_speed         Fan speed for medium cooling state in range
                                0 to 255, default 152 (60% PWM duty cycle)
        fan_temp2_speed         Fan speed for high cooling state in range
                                0 to 255, default 204 (80% PWM duty cycle)
        fan_temp3_speed         Fan speed for max cooling state in range
                                0 to 255, default 255 (100% PWM duty cycle)


Name:   pwm-ir-tx
Info:   Use GPIO pin as pwm-assisted infrared transmitter output.
        This is an alternative to "gpio-ir-tx". pwm-ir-tx makes use
        of PWM0 to reduce the CPU load during transmission compared to
        gpio-ir-tx which uses bit-banging.
        Legal pin,function combinations are:
          12,4(Alt0) 18,2(Alt5) 40,4(Alt0) 52,5(Alt1)
Load:   dtoverlay=pwm-ir-tx,<param>=<val>
Params: gpio_pin                Output GPIO (default 18)

        func                    Pin function (default 2 = Alt5)


Name:   pwm-pio
Info:   Configures a GPIO pin as PIO-assisted PWM output. Unlike hardware PWM,
        this can be used on any RP1 GPIO in bank 0 (0-27). Up to 4 are
        supported, assuming nothing else is using PIO. Pi 5 only.
Load:   dtoverlay=pwm-pio,<param>=<val>
Params: gpio                    Output GPIO (0-27, default 4)


Name:   pwm1
Info:   Configures one or two PWM channel on PWM1 (BCM2711 only)
        N.B.:
          1) The onboard analogue audio output uses both PWM channels.
          2) So be careful mixing audio and PWM.
        Note that even when only one pin is enabled, both channels are available
        from the PWM driver, so be careful to use the correct one.
Load:   dtoverlay=pwm1,<param>=<val>
Params: clock                   PWM clock frequency (informational)
        pins_40                 Enable channel 0 (PWM1_0) on GPIO 40
        pins_41                 Enable channel 1 (PWM1_1) on GPIO 41
        pins_40_41              Enable channels 0 (PWM1_0) and 1 (PW1_1) on
                                GPIOs 40 and 41 (default)
        pull_up                 Enable pull-ups on the PWM pins (default)
        pull_down               Enable pull-downs on the PWM pins
        pull_off                Disable pulls on the PWM pins


Name:   qca7000
Info:   Evaluation Board for PLC Stamp micro
        This uses spi0 and a separate GPIO interrupt to connect the QCA7000.
Load:   dtoverlay=qca7000,<param>=<val>
Params: int_pin                 GPIO pin for interrupt signal (default 23)

        speed                   SPI bus speed (default 12 MHz)


Name:   qca7000-uart0
Info:   Evaluation Board for PLC Stamp micro (UART)
        This uses uart0/ttyAMA0 over GPIOs 14 & 15 to connect the QCA7000.
        But it requires disabling of onboard Bluetooth on
        Pi 3B, 3B+, 3A+, 4B and Zero W.
Load:   dtoverlay=qca7000-uart0,<param>=<val>
Params: baudrate                Set the baudrate for the UART (default
                                "115200")


Name:   ramoops
Info:   Enable the preservation of crash logs across a reboot. With
        systemd-pstore enabled (as it is on Raspberry Pi OS) the crash logs
        are moved to /var/lib/systemd/pstore/ on reboot.
Load:   dtoverlay=ramoops,<param>=<val>
Params: base-addr               Where to place the capture buffer (default
                                0x0b000000)
        total-size              How much memory to allocate altogether (in
                                bytes - default 64kB)
        record-size             How much space to use for each capture, i.e.
                                total-size / record-size = number of captures
                                (default 16kB)
        console-size            Size of non-panic dmesg captures (default 0)


Name:   ramoops-pi4
Info:   The version of the ramoops overlay for the Pi 4 family. It should be
        loaded automatically if dtoverlay=ramoops is specified on a Pi 4.
Load:   dtoverlay=ramoops-pi4,<param>=<val>
Params: base-addr               Where to place the capture buffer (default
                                0x0b000000)
        total-size              How much memory to allocate altogether (in
                                bytes - default 64kB)
        record-size             How much space to use for each capture, i.e.
                                total-size / record-size = number of captures
                                (default 16kB)
        console-size            Size of non-panic dmesg captures (default 0)


Name:   rootmaster
Info:   Overlay for OpenHydroponics RootMaster board.
        https://openhydroponics.com/hw/rootmaster
Load:   dtoverlay=rootmaster
Params: <None>


Name:   rotary-encoder
Info:   Overlay for GPIO connected rotary encoder.
Load:   dtoverlay=rotary-encoder,<param>=<val>
Params: pin_a                   GPIO connected to rotary encoder channel A
                                (default 4).
        pin_b                   GPIO connected to rotary encoder channel B
                                (default 17).
        relative_axis           register a relative axis rather than an
                                absolute one. Relative axis will only
                                generate +1/-1 events on the input device,
                                hence no steps need to be passed.
        linux_axis              the input subsystem axis to map to this
                                rotary encoder. Defaults to 0 (ABS_X / REL_X)
        rollover                Automatic rollover when the rotary value
                                becomes greater than the specified steps or
                                smaller than 0. For absolute axis only.
        steps-per-period        Number of steps (stable states) per period.
                                The values have the following meaning:
                                1: Full-period mode (default)
                                2: Half-period mode
                                4: Quarter-period mode
        steps                   Number of steps in a full turnaround of the
                                encoder. Only relevant for absolute axis.
                                Defaults to 24 which is a typical value for
                                such devices.
        wakeup                  Boolean, rotary encoder can wake up the
                                system.
        encoding                String, the method used to encode steps.
                                Supported are "gray" (the default and more
                                common) and "binary".


Name:   rpi-backlight
Info:   Raspberry Pi official display backlight driver
Load:   dtoverlay=rpi-backlight
Params: <None>


Name:   rpi-cirrus-wm5102
Info:   This overlay has been renamed to cirrus-wm5102
Load:   <Deprecated>


Name:   rpi-codeczero
Info:   Configures the Raspberry Pi Codec Zero sound card
Load:   dtoverlay=rpi-codeczero
Params: <None>


Name:   rpi-dac
Info:   This overlay has been renamed to i2s-dac.
Load:   <Deprecated>


Name:   rpi-dacplus
Info:   Configures the Raspberry Pi DAC+ card
Load:   dtoverlay=rpi-dacplus,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                digital volume control. Enable by adding
                                "dtparam=24db_digital_gain" to config.txt
                                before any "dtoverlay" lines.
                                The default behaviour is that the digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the digital volume control is set to a value
                                that does not result in clipping/distortion!


Name:   rpi-dacpro
Info:   Configures the Raspberry Pi DAC Pro sound card
Load:   dtoverlay=rpi-dacpro,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                digital volume control. Enable by adding
                                "dtparam=24db_digital_gain" to config.txt
                                before any "dtoverlay" lines.
                                The default behaviour is that the digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the digital volume control is set to a value
                                that does not result in clipping/distortion!


Name:   rpi-digiampplus
Info:   Configures the Raspberry Pi DigiAMP+ sound card
Load:   dtoverlay=rpi-digiampplus,<param>=<val>
Params: 24db_digital_gain       Allow gain to be applied via the PCM512x codec
                                digital volume control. Enable by adding
                                "dtparam=24db_digital_gain" to config.txt
                                before any "dtoverlay" lines.
                                The default behaviour is that the digital
                                volume control is limited to a maximum of
                                0dB. ie. it can attenuate but not provide
                                gain. For most users, this will be desired
                                as it will prevent clipping. By appending
                                the 24db_digital_gain parameter, the digital
                                volume control will allow up to 24dB of
                                gain. If this parameter is enabled, it is the
                                responsibility of the user to ensure that
                                the digital volume control is set to a value
                                that does not result in clipping/distortion!
        auto_mute_amp           If specified, unmute/mute the DigiAMP+ when
                                starting/stopping audio playback (default "on").
        unmute_amp              If specified, unmute the DigiAMP+ amp once when
                                the DAC driver module loads (default "off").


Name:   rpi-display
Info:   This overlay has been renamed to watterott-display
Load:   <Deprecated>


Name:   rpi-ft5406
Info:   Official Raspberry Pi display touchscreen
Load:   dtoverlay=rpi-ft5406,<param>=<val>
Params: touchscreen-size-x      Touchscreen X resolution (default 800)
        touchscreen-size-y      Touchscreen Y resolution (default 480);
        touchscreen-inverted-x  Invert touchscreen X coordinates (default 0);
        touchscreen-inverted-y  Invert touchscreen Y coordinates (default 0);
        touchscreen-swapped-x-y Swap X and Y cordinates (default 0);


Name:   rpi-fw-uart
Info:   Configures the firmware software UART driver.
        This driver requires exclusive usage of the second VPU core. The
        following config.txt entries should be set when this driver is used.
        dtparam=audio=off
        isp_use_vpu0=1
Load:   dtoverlay=rpi-fw-uart,<param>[=<val>]
Params: txd0_pin                GPIO pin for TXD0 (any free - default 20)

        rxd0_pin                GPIO pin for RXD0 (any free - default 21)


Name:   rpi-poe
Info:   Raspberry Pi PoE HAT fan
Load:   dtoverlay=rpi-poe,<param>[=<val>]
Params: poe_fan_temp0           Temperature (in millicelcius) at which the fan
                                turns on (default 40000)
        poe_fan_temp0_hyst      Temperature delta (in millicelcius) at which
                                the fan turns off (default 2000)
        poe_fan_temp1           Temperature (in millicelcius) at which the fan
                                speeds up (default 45000)
        poe_fan_temp1_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 2000)
        poe_fan_temp2           Temperature (in millicelcius) at which the fan
                                speeds up (default 50000)
        poe_fan_temp2_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 2000)
        poe_fan_temp3           Temperature (in millicelcius) at which the fan
                                speeds up (default 55000)
        poe_fan_temp3_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 5000)
        i2c                     Control the fan via Linux I2C drivers instead of
                                the firmware.


Name:   rpi-poe-plus
Info:   Raspberry Pi PoE+ HAT fan
Load:   dtoverlay=rpi-poe-plus,<param>[=<val>]
Params: poe_fan_temp0           Temperature (in millicelcius) at which the fan
                                turns on (default 40000)
        poe_fan_temp0_hyst      Temperature delta (in millicelcius) at which
                                the fan turns off (default 2000)
        poe_fan_temp1           Temperature (in millicelcius) at which the fan
                                speeds up (default 45000)
        poe_fan_temp1_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 2000)
        poe_fan_temp2           Temperature (in millicelcius) at which the fan
                                speeds up (default 50000)
        poe_fan_temp2_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 2000)
        poe_fan_temp3           Temperature (in millicelcius) at which the fan
                                speeds up (default 55000)
        poe_fan_temp3_hyst      Temperature delta (in millicelcius) at which
                                the fan slows down (default 5000)
        i2c                     Control the fan via Linux I2C drivers instead of
                                the firmware.


Name:   rpi-proto
Info:   This overlay has been renamed to proto-codec.
Load:   <Deprecated>


Name:   rpi-sense
Info:   Raspberry Pi Sense HAT
Load:   dtoverlay=rpi-sense
Params: <None>


Name:   rpi-sense-v2
Info:   Raspberry Pi Sense HAT v2
Load:   dtoverlay=rpi-sense-v2
Params: <None>


Name:   rpi-tv
Info:   Raspberry Pi TV HAT
Load:   dtoverlay=rpi-tv
Params: <None>


Name:   rpivid-v4l2
Info:   This overlay has been deprecated and deleted as the V4L2 stateless
        video decoder driver is enabled by default.
Load:   <Deprecated>


Name:   rra-digidac1-wm8741-audio
Info:   Configures the Red Rocks Audio DigiDAC1 soundcard
Load:   dtoverlay=rra-digidac1-wm8741-audio
Params: <None>


Name:   sainsmart18
Info:   Overlay for the SPI-connected Sainsmart 1.8" display (based on the
        ST7735R chip).
Load:   dtoverlay=sainsmart18,<param>=<val>
Params: rotate                  Display rotation {0,90,180,270}
        speed                   SPI bus speed in Hz (default 4000000)
        fps                     Display frame rate in Hz
        bgr                     Enable BGR mode (default off)
        debug                   Debug output level {0-7}
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)


Name:   sc16is750-i2c
Info:   Overlay for the NXP SC16IS750 UART with I2C Interface
        Enables the chip on I2C1 at 0x48 (or the "addr" parameter value). To
        select another address, please refer to table 10 in reference manual.
Load:   dtoverlay=sc16is750-i2c,<param>=<val>
Params: int_pin                 GPIO used for IRQ (default 24)
        addr                    Address (default 0x48)
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"
        xtal                    On-board crystal frequency (default 14745600)


Name:   sc16is750-spi0
Info:   Overlay for the NXP SC16IS750 UART with SPI Interface
        Enables the chip on SPI0.
Load:   dtoverlay=sc16is750-spi0,<param>=<val>
Params: int_pin                 GPIO used for IRQ (default 24)
        xtal                    On-board crystal frequency (default 14745600)


Name:   sc16is752-i2c
Info:   Overlay for the NXP SC16IS752 dual UART with I2C Interface
        Enables the chip on I2C1 at 0x48 (or the "addr" parameter value). To
        select another address, please refer to table 10 in reference manual.
Load:   dtoverlay=sc16is752-i2c,<param>=<val>
Params: int_pin                 GPIO used for IRQ (default 24)
        addr                    Address (default 0x48)
        i2c-bus                 Supports all the standard I2C bus selection
                                parameters - see "dtoverlay -h i2c-bus"
        xtal                    On-board crystal frequency (default 14745600)


Name:   sc16is752-spi0
Info:   Overlay for the NXP SC16IS752 Dual UART with SPI Interface
        Enables the chip on SPI0.
Load:   dtoverlay=sc16is752-spi0,<param>=<val>
Params: int_pin                 GPIO used for IRQ (default 24)
        xtal                    On-board crystal frequency (default 14745600)


Name:   sc16is752-spi1
Info:   Overlay for the NXP SC16IS752 Dual UART with SPI Interface
        Enables the chip on SPI1.
        N.B.: spi1 is only accessible on devices with a 40pin header, eg:
              A+, B+, Zero and PI2 B; as well as the Compute Module.

Load:   dtoverlay=sc16is752-spi1,<param>=<val>
Params: int_pin                 GPIO used for IRQ (default 24)
        xtal                    On-board crystal frequency (default 14745600)


Name:   sdhost
Info:   Selects the bcm2835-sdhost SD/MMC driver, optionally with overclock.
        N.B. This overlay is designed for situations where the mmc driver is
        the default, so it disables the other (mmc) interface - this will kill
        WLAN on a Pi3. If this isn't what you want, either use the sdtweak
        overlay or the new sd_* dtparams of the base DTBs.
Load:   dtoverlay=sdhost,<param>=<val>
Params: overclock_50            Clock (in MHz) to use when the MMC framework
                                requests 50MHz

        force_pio               Disable DMA support (default off)

        pio_limit               Number of blocks above which to use DMA
                                (default 1)

        debug                   Enable debug output (default off)


Name:   sdio
Info:   Selects the bcm2835-sdhost SD/MMC driver, optionally with overclock,
        and enables SDIO via GPIOs 22-27. An example of use in 1-bit mode is
        "dtoverlay=sdio,bus_width=1,gpios_22_25"
Load:   dtoverlay=sdio,<param>=<val>
Params: sdio_overclock          SDIO Clock (in MHz) to use when the MMC
                                framework requests 50MHz

        poll_once               Disable SDIO-device polling every second
                                (default on: polling once at boot-time)

        bus_width               Set the SDIO host bus width (default 4 bits)

        gpios_22_25             Select GPIOs 22-25 for 1-bit mode. Must be used
                                with bus_width=1. This replaces the sdio-1bit
                                overlay, which is now deprecated.

        gpios_34_37             Select GPIOs 34-37 for 1-bit mode. Must be used
                                with bus_width=1.

        gpios_34_39             Select GPIOs 34-39 for 4-bit mode. Must be used
                                with bus_width=4 (the default).


Name:   sdio-1bit
Info:   This overlay is now deprecated. Use
        "dtoverlay=sdio,bus_width=1,gpios_22_25" instead.
Load:   <Deprecated>


Name:   sdio-pi5
Info:   Selects the rp1_mmc0 interface and enables it on GPIOs 22-27.
        Pi 5 only.
Load:   dtoverlay=sdio-pi5
Params: <None>


Name:   sdtweak
Info:   This overlay is now deprecated. Use the sd_* dtparams in the
        base DTB, e.g. "dtoverlay=sdtweak,poll_once" becomes
        "dtparam=sd_poll_once".
Load:   <Deprecated>


Name:   seeed-can-fd-hat-v1
Info:   Overlay for Seeed Studio CAN BUS FD HAT with two CAN FD
        channels without RTC. Use this overlay if your HAT has no
        battery holder.
        https://www.seeedstudio.com/2-Channel-CAN-BUS-FD-Shield-for-Raspberry-Pi-p-4072.html
Load:   dtoverlay=seeed-can-fd-hat-v1
Params: <None>


Name:   seeed-can-fd-hat-v2
Info:   Overlay for Seeed Studio CAN BUS FD HAT with two CAN FD
        channels and an RTC. Use this overlay if your HAT has a
        battery holder.
        https://www.seeedstudio.com/CAN-BUS-FD-HAT-for-Raspberry-Pi-p-4742.html
Load:   dtoverlay=seeed-can-fd-hat-v2
Params: <None>


Name:   sh1106-spi
Info:   Overlay for SH1106 OLED via SPI using fbtft staging driver.
Load:   dtoverlay=sh1106-spi,<param>=<val>
Params: speed                   SPI bus speed (default 4000000)
        rotate                  Display rotation (0, 90, 180 or 270; default 0)
        fps                     Delay between frame updates (default 25)
        debug                   Debug output level (0-7; default 0)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)
        height                  Display height (32 or 64; default 64)


Name:   si446x-spi0
Info:   Overlay for Si446x UHF Transceiver via SPI using si446x driver.
        The driver is currently out-of-tree at
        https://github.com/sunipkmukherjee/silabs.git
Load:   dtoverlay=si446x-spi0,<param>=<val>
Params: speed                   SPI bus speed (default 4000000)
        int_pin                 GPIO pin for interrupts (default 17)
        reset_pin               GPIO pin for RESET (default 27)


Name:   smi
Info:   Enables the Secondary Memory Interface peripheral. Uses GPIOs 2-25!
Load:   dtoverlay=smi
Params: <None>


Name:   smi-dev
Info:   Enables the userspace interface for the SMI driver
Load:   dtoverlay=smi-dev
Params: <None>


Name:   smi-nand
Info:   Enables access to NAND flash via the SMI interface
Load:   dtoverlay=smi-nand
Params: <None>


Name:   spi-gpio35-39
Info:   Move SPI function block to GPIO 35 to 39
Load:   dtoverlay=spi-gpio35-39
Params: <None>


Name:   spi-gpio40-45
Info:   Move SPI function block to GPIOs 40 to 45
Load:   dtoverlay=spi-gpio40-45
Params: <None>


Name:   spi-rtc
Info:   Adds support for a number of SPI Real Time Clock devices
Load:   dtoverlay=spi-rtc,<param>=<val>
Params: ds3232                  Select the DS3232 device
        ds3234                  Select the DS3234 device
        pcf2123                 Select the PCF2123 device

        spi0_0                  Use spi0.0 (default)
        spi0_1                  Use spi0.1
        spi1_0                  Use spi1.0
        spi1_1                  Use spi1.1
        spi2_0                  Use spi2.0
        spi2_1                  Use spi2.1
        cs_high                 This device requires an active-high CS


Name:   spi0-0cs
Info:   Don't claim any CS pins for SPI0. Requires a device with its chip
        select permanently enabled, but frees a GPIO for e.g. a DPI display.
Load:   dtoverlay=spi0-0cs,<param>=<val>
Params: no_miso                 Don't claim and use the MISO pin (9), freeing
                                it for other uses.


Name:   spi0-1cs
Info:   Only use one CS pin for SPI0
Load:   dtoverlay=spi0-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 8)
        no_miso                 Don't claim and use the MISO pin (9), freeing
                                it for other uses.


Name:   spi0-1cs-inverted
Info:   Only use one CS pin for SPI0 and set to active-high
Load:   dtoverlay=spi0-1cs-inverted,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 8)
        no_miso                 Don't claim and use the MISO pin (9), freeing
                                it for other uses.


Name:   spi0-2cs
Info:   Change the CS pins for SPI0
Load:   dtoverlay=spi0-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 8)
        cs1_pin                 GPIO pin for CS1 (default 7)
        no_miso                 Don't claim and use the MISO pin (9), freeing
                                it for other uses.


Name:   spi0-cs
Info:   This overlay has been renamed spi0-2cs, keeping spi0-cs as an
        alias for backwards compatibility.
Load:   <Deprecated>


Name:   spi0-hw-cs
Info:   This overlay has been deprecated and removed because it is no longer
        necessary and has been seen to prevent spi0 from working.
Load:   <Deprecated>


Name:   spi1-1cs
Info:   Enables spi1 with a single chip select (CS) line and associated spidev
        dev node. The gpio pin number for the CS line and spidev device node
        creation are configurable.
        N.B.: spi1 is not accessible on old Pis without a 40-pin header.
Load:   dtoverlay=spi1-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 18 - BCM SPI1_CE0).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.0 (default
                                is 'on' or enabled).


Name:   spi1-2cs
Info:   Enables spi1 with two chip select (CS) lines and associated spidev
        dev nodes. The gpio pin numbers for the CS lines and spidev device node
        creation are configurable.
        N.B.: spi1 is not accessible on old Pis without a 40-pin header.
Load:   dtoverlay=spi1-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 18 - BCM SPI1_CE0).
        cs1_pin                 GPIO pin for CS1 (default 17 - BCM SPI1_CE1).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.1 (default
                                is 'on' or enabled).


Name:   spi1-3cs
Info:   Enables spi1 with three chip select (CS) lines and associated spidev
        dev nodes. The gpio pin numbers for the CS lines and spidev device node
        creation are configurable.
        N.B.: spi1 is not accessible on old Pis without a 40-pin header.
Load:   dtoverlay=spi1-3cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 18 - BCM SPI1_CE0).
        cs1_pin                 GPIO pin for CS1 (default 17 - BCM SPI1_CE1).
        cs2_pin                 GPIO pin for CS2 (default 16 - BCM SPI1_CE2).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.1 (default
                                is 'on' or enabled).
        cs2_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev1.2 (default
                                is 'on' or enabled).


Name:   spi2-1cs
Info:   Enables spi2 on GPIOs 40-42 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin number for the CS line and
        spidev device node creation are configurable. spi2-2cs-pi5 is
        substituted on a Pi 5.
        N.B.: spi2 is only accessible with the Compute Module or Pi 5.
Load:   dtoverlay=spi2-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 43 - BCM SPI2_CE0).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.0 (default
                                is 'on' or enabled).


Name:   spi2-1cs-pi5
Info:   Enables spi2 on GPIOs 1-3 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin number for the CS line and
        spidev device node creation are configurable. Pi 5 only.
Load:   dtoverlay=spi2-1cs-pi5,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 0).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.0 (default
                                is 'on' or enabled).


Name:   spi2-2cs
Info:   Enables spi2 on GPIOs 40-42 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. spi2-2cs-pi5 is
        substituted on a Pi 5.
        N.B.: spi2 is only accessible with the Compute Module or Pi 5.
Load:   dtoverlay=spi2-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 43 - BCM SPI2_CE0).
        cs1_pin                 GPIO pin for CS1 (default 44 - BCM SPI2_CE1).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.1 (default
                                is 'on' or enabled).


Name:   spi2-2cs-pi5
Info:   Enables spi2 on GPIOs 1-3 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. Pi 5 only.
Load:   dtoverlay=spi2-2cs-pi5,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 0).
        cs1_pin                 GPIO pin for CS1 (default 24).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.1 (default
                                is 'on' or enabled).


Name:   spi2-3cs
Info:   Enables spi2 on GPIOs 40-42 with three chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable.
        N.B.: spi2 is only accessible with the Compute Module or Pi 5.
Load:   dtoverlay=spi2-3cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 43 - BCM SPI2_CE0).
        cs1_pin                 GPIO pin for CS1 (default 44 - BCM SPI2_CE1).
        cs2_pin                 GPIO pin for CS2 (default 45 - BCM SPI2_CE2).
        cs0_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.1 (default
                                is 'on' or enabled).
        cs2_spidev              Set to 'off' to stop the creation of a
                                userspace device node /dev/spidev2.2 (default
                                is 'on' or enabled).


Name:   spi3-1cs
Info:   Enables spi3 on GPIOs 1-3 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin number for the CS line and
        spidev device node creation are configurable. BCM2711 only,
        spi3-1cs-pi5 is substituted on Pi 5.
Load:   dtoverlay=spi3-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 0 - BCM SPI3_CE0).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.0 (default
                                is 'on' or enabled).


Name:   spi3-1cs-pi5
Info:   Enables spi3 on GPIOs 5-7 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin number for the CS line and
        spidev device node creation are configurable. Pi 5 only.
Load:   dtoverlay=spi3-1cs-pi5,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 4).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.0 (default
                                is 'on' or enabled).


Name:   spi3-2cs
Info:   Enables spi3 on GPIO2 1-3 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. BCM2711 only,
        spi3-2cs-pi5 is substituted on Pi 5.
Load:   dtoverlay=spi3-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 0 - BCM SPI3_CE0).
        cs1_pin                 GPIO pin for CS1 (default 24 - BCM SPI3_CE1).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.1 (default
                                is 'on' or enabled).


Name:   spi3-2cs-pi5
Info:   Enables spi3 on GPIOs 5-7 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. Pi 5 only.
Load:   dtoverlay=spi3-2cs-pi5,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 4).
        cs1_pin                 GPIO pin for CS1 (default 25).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev3.1 (default
                                is 'on' or enabled).


Name:   spi4-1cs
Info:   Enables spi4 on GPIOs 5-7 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin number for the CS line and
        spidev device node creation are configurable. BCM2711 only.
Load:   dtoverlay=spi4-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 4 - BCM SPI4_CE0).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev4.0 (default
                                is 'on' or enabled).


Name:   spi4-2cs
Info:   Enables spi4 on GPIOs 5-6 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. BCM2711 only.
Load:   dtoverlay=spi4-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 4 - BCM SPI4_CE0).
        cs1_pin                 GPIO pin for CS1 (default 25 - BCM SPI4_CE1).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev4.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev4.1 (default
                                is 'on' or enabled).


Name:   spi5-1cs
Info:   Enables spi5 on GPIOs 13-15 with a single chip select (CS) line and
        associated spidev dev node. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. BCM2711 and Pi 5.
Load:   dtoverlay=spi5-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 12).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev5.0 (default
                                is 'on' or enabled).


Name:   spi5-1cs-pi5
Info:   See spi5-1cs


Name:   spi5-2cs
Info:   Enables spi5 on GPIOs 13-15 with two chip select (CS) lines and
        associated spidev dev nodes. The gpio pin numbers for the CS lines and
        spidev device node creation are configurable. BCM2711 and Pi 5.
Load:   dtoverlay=spi5-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 12).
        cs1_pin                 GPIO pin for CS1 (default 26).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev5.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev5.1 (default
                                is 'on' or enabled).


Name:   spi5-2cs-pi5
Info:   See spi5-2cs


Name:   spi6-1cs
Info:   Enables spi6 with a single chip select (CS) line and associated spidev
        dev node. The gpio pin number for the CS line and spidev device node
        creation are configurable. BCM2711 only.
Load:   dtoverlay=spi6-1cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 18 - BCM SPI6_CE0).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev6.0 (default
                                is 'on' or enabled).


Name:   spi6-2cs
Info:   Enables spi6 with two chip select (CS) lines and associated spidev
        dev nodes. The gpio pin numbers for the CS lines and spidev device node
        creation are configurable. BCM2711 only.
Load:   dtoverlay=spi6-2cs,<param>=<val>
Params: cs0_pin                 GPIO pin for CS0 (default 18 - BCM SPI6_CE0).
        cs1_pin                 GPIO pin for CS1 (default 27 - BCM SPI6_CE1).
        cs0_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev6.0 (default
                                is 'on' or enabled).
        cs1_spidev              Set to 'off' to prevent the creation of a
                                userspace device node /dev/spidev6.1 (default
                                is 'on' or enabled).


Name:   ssd1306
Info:   Overlay for activation of SSD1306 over I2C OLED display framebuffer.
Load:   dtoverlay=ssd1306,<param>=<val>
Params: address                 Location in display memory of first character.
                                (default=0)
        width                   Width of display. (default=128)
        height                  Height of display. (default=64)
        offset                  virtual channel a. (default=0)
        normal                  Has no effect on displays tested. (default=not
                                set)
        sequential              Set this if every other scan line is missing.
                                (default=not set)
        remapped                Set this if display is garbled. (default=not
                                set)
        inverted                Set this if display is inverted and mirrored.
                                (default=not set)

        Examples:
        Typical usage for 128x64 display: dtoverlay=ssd1306,inverted

        Typical usage for 128x32 display: dtoverlay=ssd1306,inverted,sequential

        i2c_baudrate=400000 will speed up the display.

        i2c_baudrate=1000000 seems to work even though it's not officially
        supported by the hardware, and is faster still.

        For more information refer to the device datasheet at:
        https://cdn-shop.adafruit.com/datasheets/SSD1306.pdf


Name:   ssd1306-spi
Info:   Overlay for SSD1306 OLED via SPI using fbtft staging driver.
Load:   dtoverlay=ssd1306-spi,<param>=<val>
Params: speed                   SPI bus speed (default 10000000)
        rotate                  Display rotation (0, 90, 180 or 270; default 0)
        fps                     Delay between frame updates (default 25)
        debug                   Debug output level (0-7; default 0)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)
        height                  Display height (32 or 64; default 64)
        inverted                Set this if display is inverted and mirrored.
                                (default=not set)


Name:   ssd1327-spi
Info:   Overlay for SSD1327 OLED via SPI using the DRM ssd130x driver.
Load:   dtoverlay=ssd1327-spi,<param>=<val>
Params: speed                   SPI bus speed (default 4500000)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)


Name:   ssd1331-spi
Info:   Overlay for SSD1331 OLED via SPI using fbtft staging driver.
Load:   dtoverlay=ssd1331-spi,<param>=<val>
Params: speed                   SPI bus speed (default 4500000)
        rotate                  Display rotation (0, 90, 180 or 270; default 0)
        fps                     Delay between frame updates (default 25)
        debug                   Debug output level (0-7; default 0)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)


Name:   ssd1351-spi
Info:   Overlay for SSD1351 OLED via SPI using fbtft staging driver.
Load:   dtoverlay=ssd1351-spi,<param>=<val>
Params: speed                   SPI bus speed (default 4500000)
        rotate                  Display rotation (0, 90, 180 or 270; default 0)
        fps                     Delay between frame updates (default 25)
        debug                   Debug output level (0-7; default 0)
        dc_pin                  GPIO pin for D/C (default 24)
        reset_pin               GPIO pin for RESET (default 25)


Name:   sunfounder-pipower3
Info:   Overlay for SunFounder PiPower 3
Load:   dtoverlay=sunfounder-pipower3,<param>=<val>
Params: poweroff_pin            Change poweroff pin (default 26)


Name:   sunfounder-pironman5
Info:   Overlay for SunFounder Pironman 5
Load:   dtoverlay=sunfounder-pironman5,<param>=<val>
Params: ir                      Enable IR or not (on or off, default on)
        ir_pins                 Change IR receiver pin (default 13)


Name:   superaudioboard
Info:   Configures the SuperAudioBoard sound card
Load:   dtoverlay=superaudioboard,<param>=<val>
Params: gpiopin                 GPIO pin for codec reset


Name:   sx150x
Info:   Configures the Semtech SX150X I2C GPIO expanders.
Load:   dtoverlay=sx150x,<param>=<val>
Params: sx150<x>-<n>-<m>        Enables SX150X device on I2C#<n> with slave
                                address <m>. <x> may be 1-9. <n> may be 0 or 1.
                                Permissible values of <m> (which is denoted in
                                hex) depend on the device variant. For SX1501,
                                SX1502, SX1504 and SX1505, <m> may be 20 or 21.
                                For SX1503 and SX1506, <m> may be 20. For
                                SX1507 and SX1509, <m> may be 3E, 3F, 70 or 71.
                                For SX1508, <m> may be 20, 21, 22 or 23.

        sx150<x>-<n>-<m>-int-gpio
                                Integer, enables interrupts on SX150X device on
                                I2C#<n> with slave address <m>, specifies
                                the GPIO pin to which NINT output of SX150X is
                                connected.


Name:   tc358743
Info:   Toshiba TC358743 HDMI to CSI-2 bridge chip.
        Uses Unicam 1, which is the standard camera connector on most Pi
        variants.
Load:   dtoverlay=tc358743,<param>=<val>
Params: 4lane                   Use 4 lanes (only applicable to Compute Modules
                                CAM1 connector).

        link-frequency          Set the link frequency. Only values of 297000000
                                (574Mbit/s) and 486000000 (972Mbit/s - default)
                                are supported by the driver.
        media-controller        Configure use of Media Controller API for
                                configuring the sensor (default off)
        cam0                    Adopt the default configuration for CAM0 on a
                                Compute Module (CSI0, i2c_vc, and cam0_reg).


Name:   tc358743-audio
Info:   Used in combination with the tc358743-fast overlay to route the audio
        from the TC358743 over I2S to the Pi.
        Wiring is LRCK/WFS to GPIO 19, BCK/SCK to GPIO 18, and DATA/SD to GPIO
        20.
Load:   dtoverlay=tc358743-audio,<param>=<val>
Params: card-name               Override the default, "tc358743", card name.


Name:   tinylcd35
Info:   3.5" Color TFT Display by www.tinylcd.com
        Options: Touch, RTC, keypad
Load:   dtoverlay=tinylcd35,<param>=<val>
Params: speed                   Display SPI bus speed

        rotate                  Display rotation {0,90,180,270}

        fps                     Delay between frame updates

        debug                   Debug output level {0-7}

        touch                   Enable touch panel

        touchgpio               Touch controller IRQ GPIO

        xohms                   Touchpanel: Resistance of X-plate in ohms

        rtc-pcf                 PCF8563 Real Time Clock

        rtc-ds                  DS1307 Real Time Clock

        keypad                  Enable keypad

        Examples:
            Display with touchpanel, PCF8563 RTC and keypad:
                dtoverlay=tinylcd35,touch,rtc-pcf,keypad
            Old touch display:
                dtoverlay=tinylcd35,touch,touchgpio=3


Name:   tpm-slb9670
Info:   Enables support for Infineon SLB9670 Trusted Platform Module add-on
        boards, which can be used as a secure key storage and hwrng,
        available as "Iridium SLB9670" by Infineon and "LetsTrust TPM" by pi3g.
Load:   dtoverlay=tpm-slb9670
Params: <None>


Name:   tpm-slb9673
Info:   Enables support for Infineon SLB9673 Trusted Platform Module add-on
        boards, which can be used as a secure key storage and hwrng
        via the I2C protocol.
Load:   dtoverlay=tpm-slb9673
Params: <None>


Name:   uart0
Info:   Change the pin usage of uart0
Load:   dtoverlay=uart0,<param>=<val>
Params: txd0_pin                GPIO pin for TXD0 (14, 32 or 36 - default 14)

        rxd0_pin                GPIO pin for RXD0 (15, 33 or 37 - default 15)

        pin_func                Alternative pin function - 4(Alt0) for 14&15,
                                7(Alt3) for 32&33, 6(Alt2) for 36&37


Name:   uart0-pi5
Info:   Enable uart 0 on GPIOs 14-15. Pi 5 only.
Load:   dtoverlay=uart0-pi5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 16-17 (default off)


Name:   uart1
Info:   Change the pin usage of uart1
Load:   dtoverlay=uart1,<param>=<val>
Params: txd1_pin                GPIO pin for TXD1 (14, 32 or 40 - default 14)

        rxd1_pin                GPIO pin for RXD1 (15, 33 or 41 - default 15)


Name:   uart1-pi5
Info:   Enable uart 1 on GPIOs 0-1. Pi 5 only.
Load:   dtoverlay=uart1-pi5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 2-3 (default off)


Name:   uart2
Info:   Enable uart 2 on GPIOs 0-3. BCM2711 only.
Load:   dtoverlay=uart2,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 2-3 (default off)


Name:   uart2-pi5
Info:   Enable uart 2 on GPIOs 4-5. Pi 5 only.
Load:   dtoverlay=uart2-pi5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 6-7 (default off)


Name:   uart3
Info:   Enable uart 3 on GPIOs 4-7. BCM2711 only.
Load:   dtoverlay=uart3,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 6-7 (default off)


Name:   uart3-pi5
Info:   Enable uart 3 on GPIOs 8-9. Pi 5 only.
Load:   dtoverlay=uart3-pi5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 10-11 (default off)


Name:   uart4
Info:   Enable uart 4 on GPIOs 8-11. BCM2711 only.
Load:   dtoverlay=uart4,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 10-11 (default off)


Name:   uart4-pi5
Info:   Enable uart 4 on GPIOs 12-13. Pi 5 only.
Load:   dtoverlay=uart4-pi5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 14-15 (default off)


Name:   uart5
Info:   Enable uart 5 on GPIOs 12-15. BCM2711 only.
Load:   dtoverlay=uart5,<param>
Params: ctsrts                  Enable CTS/RTS on GPIOs 14-15 (default off)


Name:   udrc
Info:   Configures the NW Digital Radio UDRC Hat
Load:   dtoverlay=udrc,<param>=<val>
Params: alsaname                Name of the ALSA audio device (default "udrc")


Name:   ugreen-dabboard
Info:   Configures the ugreen-dabboard I2S overlay
        This is a simple overlay based on the simple-audio-card and the dmic
        codec. It has the speciality that it is configured to use the codec
        as a master I2S device. It works for example with the Si468x DAB
        receiver on the uGreen DABBoard.
Load:   dtoverlay=ugreen-dabboard,<param>=<val>
Params: card-name               Override the default, "dabboard", card name.


Name:   upstream
Info:   Allow usage of downstream .dtb with upstream kernel. Comprises the
        vc4-kms-v3d and dwc2 overlays.
Load:   dtoverlay=upstream
Params: <None>


Name:   upstream-aux-interrupt
Info:   This overlay has been deprecated and removed because it is no longer
        necessary.
Load:   <Deprecated>


Name:   upstream-pi4
Info:   Allow usage of downstream .dtb with upstream kernel on Pi 4. Comprises
        the vc4-kms-v3d-pi4 and dwc2 overlays.
Load:   dtoverlay=upstream-pi4
Params: <None>


Name:   vc4-fkms-v3d
Info:   Enable the kernel DRM VC4 V3D driver on top of the dispmanx
        display stack.
        NB The firmware will not allow this overlay to load on a Pi with less
        than 512MB as memory is too tight.
Load:   dtoverlay=vc4-fkms-v3d,<param>
Params: cma-512                 CMA is 512MB (needs 1GB)
        cma-448                 CMA is 448MB (needs 1GB)
        cma-384                 CMA is 384MB (needs 1GB)
        cma-320                 CMA is 320MB (needs 1GB)
        cma-256                 CMA is 256MB (needs 1GB)
        cma-192                 CMA is 192MB (needs 1GB)
        cma-128                 CMA is 128MB
        cma-96                  CMA is 96MB
        cma-64                  CMA is 64MB
        cma-size                CMA size in bytes, 4MB aligned
        cma-default             Use upstream's default value


Name:   vc4-fkms-v3d-pi4
Info:   Enable the kernel DRM VC4 V3D driver on top of the dispmanx
        display stack.
Load:   dtoverlay=vc4-fkms-v3d-pi4,<param>
Params: cma-512                 CMA is 512MB (needs 1GB)
        cma-448                 CMA is 448MB (needs 1GB)
        cma-384                 CMA is 384MB (needs 1GB)
        cma-320                 CMA is 320MB (needs 1GB)
        cma-256                 CMA is 256MB (needs 1GB)
        cma-192                 CMA is 192MB (needs 1GB)
        cma-128                 CMA is 128MB
        cma-96                  CMA is 96MB
        cma-64                  CMA is 64MB
        cma-size                CMA size in bytes, 4MB aligned
        cma-default             Use upstream's default value


Name:   vc4-kms-dpi-at056tn53v1
Info:   This overlay is now deprecated - see vc4-kms-dpi-panel,at056tn53v1
Load:   <Deprecated>


Name:   vc4-kms-dpi-generic
Info:   Enable a generic DPI display under KMS. Default timings are for the
        Adafruit Kippah with 800x480 panel and RGB666 (GPIOs 0-21)
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dpi-generic,<param>=<val>
Params: clock-frequency         Display clock frequency (Hz)
        hactive                 Horizontal active pixels
        hfp                     Horizontal front porch
        hsync                   Horizontal sync pulse width
        hbp                     Horizontal back porch
        vactive                 Vertical active lines
        vfp                     Vertical front porch
        vsync                   Vertical sync pulse width
        vbp                     Vertical back porch
        hsync-invert            Horizontal sync active low
        vsync-invert            Vertical sync active low
        de-invert               Data Enable active low
        pixclk-invert           Negative edge pixel clock
        interlaced              Use an interlaced mode (where supported)
        width-mm                Define the screen width in mm
        height-mm               Define the screen height in mm
        rgb565                  Change to RGB565 output on GPIOs 0-19
        rgb565-padhi            Change to RGB565 output on GPIOs 0-8, 12-17, and
                                20-24
        bgr666                  Change to BGR666 output on GPIOs 0-21.
        bgr666-padhi            Change to BGR666 output on GPIOs 0-9, 12-17, and
                                20-25
        rgb666-padhi            Change to RGB666 output on GPIOs 0-9, 12-17, and
                                20-25
        bgr888                  Change to BGR888 output on GPIOs 0-27
        rgb888                  Change to RGB888 output on GPIOs 0-27
        bus-format              Override the bus format for a MEDIA_BUS_FMT_*
                                value. NB also overridden by rgbXXX overrides.
        backlight-gpio          Defines a GPIO to be used for backlight control
                                (default of none).
        backlight-pwm           Defines a PWM channel to be used for backlight
                                control (default of none). NB Disables audio
                                headphone output as that also uses PWM.
        backlight-pwm-chan      Choose channel on &pwm node for backlight
                                control.
                                (default 0).
        backlight-pwm-gpio      GPIO pin to be used for the PWM backlight. See
                                pwm-2chan for valid options.
                                (default 18 - note this can only work with
                                 rgb666-padhi).
        backlight-pwm-func      Pin function of GPIO used for the PWM
                                backlight.
                                See pwm-2chan for valid options.
                                (default 2).
        backlight-def-brightness
                                Set the default brightness. Normal range 1-16.
                                (default 16).
        rotate                  Display rotation {0,90,180,270} (default 0)
        rgb-order               Allow override of RGB order from DPI.
                                Options for vc4 are "rgb", "bgr", "grb", and
                                "brg". Other values will be ignored.



Name:   vc4-kms-dpi-hyperpixel2r
Info:   Enable the KMS drivers for the Pimoroni HyperPixel2 Round DPI display.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dpi-hyperpixel2r,<param>=<val>
Params: disable-touch           Disables the touch controller
        touchscreen-inverted-x  Inverts X direction of touch controller
        touchscreen-inverted-y  Inverts Y direction of touch controller
        touchscreen-swapped-x-y Swaps X & Y axes of touch controller
        rotate                  Display rotation {0,90,180,270} (default 0)


Name:   vc4-kms-dpi-hyperpixel4
Info:   Enable the KMS drivers for the Pimoroni HyperPixel4 DPI display.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dpi-hyperpixel4,<param>=<val>
Params: disable-touch           Disables the touch controller
        touchscreen-inverted-x  Inverts X direction of touch controller
        touchscreen-inverted-y  Inverts Y direction of touch controller
        touchscreen-swapped-x-y Swaps X & Y axes of touch controller
        rotate                  Display rotation {0,90,180,270} (default 0)


Name:   vc4-kms-dpi-hyperpixel4sq
Info:   Enable the KMS drivers for the Pimoroni HyperPixel4 Square DPI display.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dpi-hyperpixel4sq,<param>=<val>
Params: disable-touch           Disables the touch controller
        touchscreen-inverted-x  Inverts X direction of touch controller
        touchscreen-inverted-y  Inverts Y direction of touch controller
        touchscreen-swapped-x-y Swaps X & Y axes of touch controller
        rotate                  Display rotation {0,90,180,270} (default 0)


Name:   vc4-kms-dpi-panel
Info:   Enable a preconfigured KMS DPI panel.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dpi-panel,<param>=<val>
Params: at056tn53v1             Enable an Innolux 5.6in VGA TFT
        kippah-7inch            Enable an Adafruit Kippah with 7inch panel.
        mzp280                  Enable a Geekworm MZP280 panel.
        backlight-gpio          Defines a GPIO to be used for backlight control
                                (default of none).
        backlight-pwm           Defines a PWM channel to be used for backlight
                                control (default of none). NB Disables audio
                                headphone output as that also uses PWM.
        backlight-pwm-chan      Choose channel on &pwm node for backlight
                                control.
                                (default 0).
        backlight-pwm-gpio      GPIO pin to be used for the PWM backlight. See
                                pwm-2chan for valid options.
                                (default 18 - note this can only work with
                                 rgb666-padhi).
        backlight-pwm-func      Pin function of GPIO used for the PWM
                                backlight.
                                See pwm-2chan for valid options.
                                (default 2).
        backlight-def-brightness
                                Set the default brightness. Normal range 1-16.
                                (default 16).
        rotate                  Display rotation {0,90,180,270} (default 0)


Name:   vc4-kms-dsi-7inch
Info:   Enable the Raspberry Pi DSI 7" screen.
        Includes the edt-ft5406 for the touchscreen element.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-7inch,<param>=<val>
Params: sizex                   Touchscreen size x (default 800)
        sizey                   Touchscreen size y (default 480)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        disable_touch           Disables the touch screen overlay driver
        dsi0                    Use DSI0 and i2c_csi_dsi0 (rather than
                                the default DSI1 and i2c_csi_dsi).


Name:   vc4-kms-dsi-generic
Info:   Enable a generic DSI display under KMS.
        Default timings are for a 840x480 RGB888 panel.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-generic,<param>=<val>
Params: clock-frequency         Display clock frequency (Hz)
        hactive                 Horizontal active pixels
        hfp                     Horizontal front porch
        hsync                   Horizontal sync pulse width
        hbp                     Horizontal back porch
        vactive                 Vertical active lines
        vfp                     Vertical front porch
        vsync                   Vertical sync pulse width
        vbp                     Vertical back porch
        width-mm                Define the screen width in mm
        height-mm               Define the screen height in mm
        rgb565                  Change to RGB565 output
        rgb666                  Change to RGB666 output
        rgb666p                 Change to RGB666 output with pixel packing
        rgb888                  Change to RGB888 output, this is the default
        one-lane                Use one DSI lane for data transmission
                                This is the default
        two-lane                Use two DSI lanes for data transmission
        three-lane              Use three DSI lanes for data transmission
                                Only supported on Pi5 and CM
        four-lane               Use four DSI lanes for data transmission
                                Only supported on Pi5 and CM
        dsi0                    Switch DSI port to DSI0
                                Only supported on Pi5 and CM


Name:   vc4-kms-dsi-ili9881-5inch
Info:   Enable the Raspberry Pi 5" ILI9881 based touchscreen panel.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-ili9881-5inch,<param>
Params: sizex                   Touchscreen size x (default 720)
        sizey                   Touchscreen size y (default 1280)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        disable_touch           Disables the touch screen overlay driver
        dsi0                    Use DSI0 and i2c_csi_dsi0 (rather than
                                the default DSI1 and i2c_csi_dsi).


Name:   vc4-kms-dsi-ili9881-7inch
Info:   Enable the Raspberry Pi 7" ILI9881 based touchscreen panel.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-ili9881-7inch,<param>
Params: sizex                   Touchscreen size x (default 720)
        sizey                   Touchscreen size y (default 1280)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        disable_touch           Disables the touch screen overlay driver
        rotation                Display rotation {0,90,180,270} (default 0)
        dsi0                    Use DSI0 and i2c_csi_dsi0 (rather than
                                the default DSI1 and i2c_csi_dsi).


Name:   vc4-kms-dsi-lt070me05000
Info:   Enable a JDI LT070ME05000 DSI display on DSI1.
        Note that this is a 4 lane DSI device, so it will only work on a Compute
        Module.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-lt070me05000,<param>
Params: reset                   GPIO for the reset signal (default 17)
        enable                  GPIO for the enable signal (default 4)
        dcdc-en                 GPIO for the DC-DC converter enable (default 5)


Name:   vc4-kms-dsi-lt070me05000-v2
Info:   Enable a JDI LT070ME05000 DSI display on DSI1 using Harlab's V2
        interface board.
        Note that this is a 4 lane DSI device, so it will only work on a Compute
        Module.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-lt070me05000-v2
Params: <None>


Name:   vc4-kms-dsi-waveshare-800x480
Info:   Enable the Waveshare 4.3" 800x480 DSI screen.
        It tries to look like the Pi 7" display, but won't accept some of the
        timings.
        Includes the edt-ft5406 for the touchscreen element.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-waveshare-800x480,<param>=<val>
Params: sizex                   Touchscreen size x (default 800)
        sizey                   Touchscreen size y (default 480)
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        disable_touch           Disables the touch screen overlay driver
        dsi0                    Use DSI0 and i2c_csi_dsi0 (rather than
                                the default DSI1 and i2c_csi_dsi).


Name:   vc4-kms-dsi-waveshare-panel
Info:   Enable a Waveshare DSI touchscreen
        Includes the Goodix driver for the touchscreen element.
        The default is for the display to be using the I2C0 option for control.
        Use the i2c1 override if using the I2C1 wiring with jumper wires from
        GPIOs 2&3 (pins 3&5).
        invx/invy/swapxy should be used with caution as the panel specifier will
        set the default inversions for that panel. Always use them after the
        panel specifier, and be aware that you may need to set them as =0, not
        just adding it.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-dsi-waveshare-panel,<param>=<val>
Params: 2_8_inch                2.8" 480x640
        3_4_inch                3.4" 800x800 round
        4_0_inch                4.0" 480x800
        4_0_inchC               4.0" 720x720
        5_0_inch                5.0" 720x1280
        6_25_inch               6.25" 720x1560
        8_8_inch                8.8" 480x1920
        7_0_inchC               7.0" C 1024x600
        7_9_inch                7.9" 400x1280
        8_0_inch                8.0" 1280x800
        10_1_inch               10.1" 1280x800
        11_9_inch               11.9" 320x1480
        13_3_inch_4lane         13.3" 1920x1080 4lane
        13_3_inch_2lane         13.3" 1920x1080 2lane
        i2c1                    Use i2c-1 with jumper wires from GPIOs 2&3
        disable_touch           Disable the touch controller
        rotation                Set the panel orientation property
        invx                    Touchscreen inverted x axis
        invy                    Touchscreen inverted y axis
        swapxy                  Touchscreen swapped x y axis
        dsi0                    Use DSI0 and i2c_csi_dsi0 (rather than
                                the default DSI1 and i2c_csi_dsi).


Name:   vc4-kms-kippah-7inch
Info:   This overlay is now deprecated - see vc4-kms-dpi-panel,kippah-7inch
Load:   <Deprecated>


Name:   vc4-kms-v3d
Info:   Enable the kernel DRM VC4 HDMI/HVS/V3D driver.
        NB The firmware will not allow this overlay to load on a Pi with less
        than 512MB as memory is too tight.
Load:   dtoverlay=vc4-kms-v3d,<param>
Params: cma-512                 CMA is 512MB (needs 1GB)
        cma-448                 CMA is 448MB (needs 1GB)
        cma-384                 CMA is 384MB (needs 1GB)
        cma-320                 CMA is 320MB (needs 1GB)
        cma-256                 CMA is 256MB (needs 1GB)
        cma-192                 CMA is 192MB (needs 1GB)
        cma-128                 CMA is 128MB
        cma-96                  CMA is 96MB
        cma-64                  CMA is 64MB
        cma-size                CMA size in bytes, 4MB aligned
        cma-default             Use upstream's default value
        audio                   Enable or disable audio over HDMI (default "on")
        noaudio                 Disable all HDMI audio (default "off")
        composite               Enable the composite output (default "off")
                                N.B. Disables all other outputs on a Pi 4.
        nohdmi                  Disable HDMI output


Name:   vc4-kms-v3d-pi4
Info:   Enable the kernel DRM VC4 HDMI/HVS/V3D driver for Pi4.
Load:   dtoverlay=vc4-kms-v3d-pi4,<param>
Params: cma-512                 CMA is 512MB
        cma-448                 CMA is 448MB
        cma-384                 CMA is 384MB
        cma-320                 CMA is 320MB
        cma-256                 CMA is 256MB
        cma-192                 CMA is 192MB
        cma-128                 CMA is 128MB
        cma-96                  CMA is 96MB
        cma-64                  CMA is 64MB
        cma-size                CMA size in bytes, 4MB aligned
        cma-default             Use upstream's default value
        audio                   Enable or disable audio over HDMI0 (default
                                "on")
        audio1                  Enable or disable audio over HDMI1 (default
                                "on")
        noaudio                 Disable all HDMI audio (default "off")
        composite               Enable the composite output (disables all other
                                outputs)
        nohdmi                  Disable both HDMI 0 & 1 outputs
        nohdmi0                 Disable HDMI 0 output
        nohdmi1                 Disable HDMI 1 output


Name:   vc4-kms-v3d-pi5
Info:   See vc4-kms-v3d-pi4 (this is the Pi 5 version)


Name:   vc4-kms-vga666
Info:   Enable the VGA666 (resistor ladder ADC) for the vc4-kms-v3d driver.
        Requires vc4-kms-v3d to be loaded.
Load:   dtoverlay=vc4-kms-vga666,<param>
Params: ddc                     Enables GPIOs 0&1 as the I2C to read the EDID
                                from the display. NB These are NOT 5V tolerant
                                GPIOs, therefore level shifters are required.


Name:   vga666
Info:   Overlay for the Fen Logic VGA666 board
        This uses GPIOs 2-21 (so no I2C), and activates the output 2-3 seconds
        after the kernel has started.
        NOT for use with vc4-kms-v3d.
Load:   dtoverlay=vga666
Params: <None>


Name:   vl805
Info:   Overlay to enable a VIA VL805 USB3 controller on CM4 carriers
        Will be loaded automatically by up-to-date firmware if "VL805=1" is
        set in the EEPROM config.
Load:   dtoverlay=vl805
Params: <None>


Name:   w1-gpio
Info:   Configures the w1-gpio Onewire interface module.
        Use this overlay if you *don't* need a GPIO to drive an external pullup.
Load:   dtoverlay=w1-gpio,<param>=<val>
Params: gpiopin                 GPIO for I/O (default "4")
        pullup                  Now enabled by default (ignored)


Name:   w1-gpio-pi5
Info:   See w1-gpio (this is the Pi 5 version)


Name:   w1-gpio-pullup
Info:   Configures the w1-gpio Onewire interface module.
        Use this overlay if you *do* need a GPIO to drive an external pullup.
Load:   dtoverlay=w1-gpio-pullup,<param>=<val>
Params: gpiopin                 GPIO for I/O (default "4")
        extpullup               GPIO for external pullup (default "5")
        pullup                  Now enabled by default (ignored)


Name:   w1-gpio-pullup-pi5
Info:   See w1-gpio-pullup (this is the Pi 5 version)


Name:   w5500
Info:   Overlay for the Wiznet W5500 Ethernet Controller on SPI0
Load:   dtoverlay=w5500,<param>=<val>
Params: int_pin                 GPIO used for INT (default 25)

        speed                   SPI bus speed (default 30000000)

        cs                      SPI bus Chip Select (default 0)


Name:   watterott-display
Info:   Watterott RPi-Display - 2.8" Touch Display
        Linux has 2 drivers that support this display and this overlay supports
        both.

        Examples:
          fbtft/fb_ili9341: dtoverlay=watterott-display
          drm/mi0283qt: dtoverlay=watterott-display,drm,backlight-pwm,rotate=180

        Some notable differences with the DRM driver compared to fbtft:
        - The display is turned on when it's first used and not on driver load
          as with fbtft. So if nothing uses the display it stays off.
        - Can run with a higher SPI clock increasing framerate. This is possible
          since the driver avoids messing up the controller configuration due to
          transmission errors by running config commands at 10MHz and only pixel
          data at full speed (occasional pixel glitch might occur).
        - PWM backlight is supported.

Load:   dtoverlay=watterott-display,<param>=<val>
Params: speed                   Display SPI bus speed
        rotate                  Display rotation {0,90,180,270}
        fps                     Delay between frame updates (fbtft only)
        debug                   Debug output level {0-7} (fbtft only)
        xohms                   Touchpanel sensitivity (X-plate resistance)
        swapxy                  Swap x and y axis
        backlight               Change backlight GPIO pin {e.g. 12, 18}
                                (fbtft only)
        drm                     Use DRM/KMS driver mi0283qt instead of fbtft.
                                Set the SPI clock to 70MHz.
                                This has to be the first parameter.
        backlight-pwm           Use pwm for backlight (drm only). NB: Disables
                                audio headphone output as that also uses PWM.


Name:   waveshare-can-fd-hat-mode-a
Info:   Overlay for the Waveshare 2-Channel Isolated CAN FD Expansion HAT
        for Raspberry Pi, Multi Protections. Use this overlay when the
        HAT is configured in Mode A (Default), with can0 on spi0.0
        and can1 on spi1.0.
        https://www.waveshare.com/2-ch-can-fd-hat.htm
Load:   dtoverlay=waveshare-can-fd-hat-mode-a
Params: <None>


Name:   waveshare-can-fd-hat-mode-b
Info:   Overlay for the Waveshare 2-Channel Isolated CAN FD Expansion HAT
        for Raspberry Pi, Multi Protections. Use this overlay when the
        HAT is configured in Mode B (requires hardware modification), with
        can0 on spi0.0 and can1 on spi0.1.
        https://www.waveshare.com/2-ch-can-fd-hat.htm
Load:   dtoverlay=waveshare-can-fd-hat-mode-b
Params: <None>


Name:   wittypi
Info:   Configures the wittypi RTC module.
Load:   dtoverlay=wittypi,<param>=<val>
Params: led_gpio                GPIO for LED (default "17")
        led_trigger             Choose which activity the LED tracks (default
                                "default-on")


Name:   wm8960-soundcard
Info:   Overlay for the Waveshare wm8960 soundcard
Load:   dtoverlay=wm8960-soundcard,<param>=<val>
Params: alsaname                Changes the card name in ALSA
        compatible              Changes the codec compatibility


Name:   ws2812-pio
Info:   Configures a GPIO pin to drive a string of WS2812 LEDS using pio. It
        can be enabled on any RP1 GPIO in bank 0 (0-27). Up to 4 are supported,
        assuming nothing else is using PIO. Pi 5 only.
Load:   dtoverlay=ws2812-pio,<param>=<val>
Params: brightness              Set the initial brightness for the LEDs. The
                                brightness can be changed at runtime by writing
                                a single byte to offset 0 of the device. Note
                                that brightness is a multiplier for the pixel
                                values, and only white pixels can reach the
                                maximum visible brightness. (range 0-255,
                                default 255)
        dev_name                The name for the /dev/ device entry. Note that
                                if the name includes '%d' it will be replaced
                                by the instance number. (default 'leds%d')
        gpio                    Output GPIO (0-27, default 4)
        num_leds                Number of LEDs (default 60)
        rgbw                    'rgbw=on' (or 'rgbw') indicates that each pixel
                                includes a white LED as well as the usual red,
                                green and blue. (default 'off')


Troubleshooting
===============

If you are experiencing problems that you think are DT-related, enable DT
diagnostic output by adding this to /boot/config.txt:

    dtdebug=on

and rebooting. Then run:

    sudo vcdbg log msg

and look for relevant messages.

Further reading
===============

This is only meant to be a quick introduction to the subject of Device Tree on
Raspberry Pi. There is a more complete explanation here:

http://www.raspberrypi.org/documentation/configuration/device-tree.md
