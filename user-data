#cloud-config
hostname: z0rlord-pi
manage_etc_hosts: true
packages:
- avahi-daemon
- hostapd
- dnsmasq
apt:
  conf: |
    Acquire {
      Check-Date "false";
    };

users:
- name: z0rlord
  groups: users,adm,dialout,audio,netdev,video,plugdev,cdrom,games,input,gpio,spi,i2c,render,sudo
  shell: /bin/bash
  lock_passwd: true
  sudo: ALL=(ALL) NOPASSWD:ALL
- name: admin
  groups: users,adm,dialout,audio,netdev,video,plugdev,cdrom,games,input,gpio,spi,i2c,render,sudo
  shell: /bin/bash
  lock_passwd: true
  sudo: ALL=(ALL) NOPASSWD:ALL

ssh_pwauth: false
disable_root: false

# Configure passwordless access
write_files:
- path: /etc/sudoers.d/90-cloud-init-users
  content: |
    # User rules for z0rlord
    z0rlord ALL=(ALL) NOPASSWD:ALL
    # User rules for admin
    admin ALL=(ALL) NOPASSWD:ALL
  permissions: '0440'
- path: /etc/ssh/sshd_config.d/99-passwordless.conf
  content: |
    # Allow empty passwords for passwordless login
    PermitEmptyPasswords yes
    PasswordAuthentication yes
    ChallengeResponseAuthentication no
  permissions: '0644'
- path: /etc/wpa_supplicant/wpa_supplicant.conf
  content: |
    country=JP
    ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
    update_config=1

    # Existing network
    network={
        ssid="KamiSama"
        psk="9e407cb9c222fe3d67af849161d8513076ad74f354f0441162692b16cbf9b04b"
        priority=1
    }

    # Z0rLords-world Hotspot
    network={
        ssid="Z0rLords-world"
        psk="z0rlord69"
        priority=2
    }
  permissions: '0600'
- path: /etc/wpa_supplicant/wpa_supplicant.conf
  content: |
    country=JP
    ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
    update_config=1

    # Existing network
    network={
        ssid="KamiSama"
        psk="9e407cb9c222fe3d67af849161d8513076ad74f354f0441162692b16cbf9b04b"
        priority=1
    }


  permissions: '0600'

timezone: Asia/Tokyo
runcmd:
- localectl set-x11-keymap "us" pc105
- setupcon -k --force || true
- usermod -aG sudo z0rlord
- usermod -aG sudo admin
- echo "z0rlord ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-z0rlord
- echo "admin ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-admin
- chmod 440 /etc/sudoers.d/90-z0rlord
- chmod 440 /etc/sudoers.d/90-admin
- passwd -d z0rlord
- passwd -d admin
- sed -i 's/#PermitEmptyPasswords no/PermitEmptyPasswords yes/' /etc/ssh/sshd_config
- sed -i 's/PermitEmptyPasswords no/PermitEmptyPasswords yes/' /etc/ssh/sshd_config
- sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
- sed -i 's/PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
- systemctl restart ssh
- systemctl restart systemd-networkd
- systemctl restart wpa_supplicant
- systemctl enable wpa_supplicant
- wpa_cli -i wlan0 reconfigure
- sleep 10
- wpa_cli -i wlan0 scan
- sleep 5
- wpa_cli -i wlan0 scan_results
- cloud-init clean --logs || true


