#cloud-config
hostname: raspberry<PERSON>
manage_etc_hosts: true
packages:
- avahi-daemon
apt:
  conf: |
    Acquire {
      Check-Date "false";
    };

users:
- name: z0rlord
  groups: users,adm,dialout,audio,netdev,video,plugdev,cdrom,games,input,gpio,spi,i2c,render,sudo
  shell: /bin/bash
  lock_passwd: false
  plain_text_passwd: raspberry
  chpasswd: { expire: false }

ssh_pwauth: true

timezone: Asia/Tokyo
runcmd:
- localectl set-x11-keymap "us" pc105
- setupcon -k --force || true


