#cloud-config
hostname: raspberry<PERSON>
manage_etc_hosts: true
packages:
- avahi-daemon
apt:
  conf: |
    Acquire {
      Check-Date "false";
    };

users:
- name: z0rlord
  groups: users,adm,dialout,audio,netdev,video,plugdev,cdrom,games,input,gpio,spi,i2c,render,sudo
  shell: /bin/bash
  lock_passwd: false
  passwd: $5$94xa9X2kFE$9uM4wmsMoX6X5lWyHQMWfQZnfhrZBKBfxEt1J2.U.B0

ssh_pwauth: true

timezone: Asia/Tokyo
runcmd:
- localectl set-x11-keymap "us" pc105
- setupcon -k --force || true


